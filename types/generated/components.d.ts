import type { Schema, Struct } from '@strapi/strapi';

export interface ChildrenTribePetraKids extends Struct.ComponentSchema {
  collectionName: 'components_children_tribe_petra_kids';
  info: {
    displayName: 'Tribe Petra Kids';
  };
  attributes: {
    Age: Schema.Attribute.Integer;
    Gender: Schema.Attribute.Enumeration<['Male', 'Female']>;
    Name: Schema.Attribute.String;
  };
}

export interface ServicesService extends Struct.ComponentSchema {
  collectionName: 'components_services_services';
  info: {
    displayName: 'Service';
    icon: 'cup';
  };
  attributes: {
    banner: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    description: Schema.Attribute.Text;
    schedule: Schema.Attribute.String;
    start_time: Schema.Attribute.DateTime;
    title: Schema.Attribute.String;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'children.tribe-petra-kids': ChildrenTribePetraKids;
      'services.service': ServicesService;
    }
  }
}
