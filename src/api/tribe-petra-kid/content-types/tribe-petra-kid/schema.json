{"kind": "collectionType", "collectionName": "tribe_petra_kids", "info": {"singularName": "tribe-petra-kid", "pluralName": "tribe-petra-kids", "displayName": "Tribe Petra Kid", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"firstname": {"type": "string"}, "surname": {"type": "string"}, "age": {"type": "integer"}, "gender": {"type": "enumeration", "enum": ["Male", "Female"]}, "year": {"type": "string", "maxLength": 4, "minLength": 4, "default": "2023"}}}