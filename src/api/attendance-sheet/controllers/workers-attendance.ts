// @ts-nocheck
import {v4 as uuidv4} from 'uuid';

export default {
  async generateLink(ctx) {
    let { service_id } = ctx.request.body;
    const serviceInfo = await strapi.documents('api::church-service.church-service').findOne({
        documentId: service_id,
        fields: ['id', 'title', 'uuid']
    });
    // if service info is empty, send error message
    if (! serviceInfo) {
        return ctx.badRequest('Not Found', { message: 'Event/Programme not found' });
    }
    // generate service uuid
    let uuid = uuidv4();
    // save uuid to service
    const entry = await strapi.documents('api::church-service.church-service').update({
        documentId: service_id,

        data: {
            // @ts-ignore
            uuid: uuid,
        }
    });
    // generate qr code
    const link = 'http://localhost:3000/attendance-sheet?tag=' + uuid;
    const qrCode = await strapi.service('api::attendance-sheet.attendance-sheet').createQRCode(link);
    ctx.body = {
        link,
        qrCode,
    };
  },
  async insertRecord(ctx) {
    let { service_uuid, worker_id } = ctx.request.body;
    // get church member information using workerId which is the email or phone number.
    const memberInfo = await strapi.documents('api::church-member.church-member').findMany({
        fields: ['id', 'firstname', 'surname', 'email', 'phone'],
        filters: {
            $or: [
              {
                email: worker_id,
              },
              {
                phone: worker_id,
              },
            ],
          },
    });
    // if member info is empty, send error message
    // @ts-ignore
    if (memberInfo.length === 0) {
        return ctx.badRequest('User Not Found', 'User information not found, please use the right Email or Phone number');
    }
    const serviceInfo = await strapi.documents('api::church-service.church-service').findMany({
        fields: ['id', 'title', 'uuid', 'start_time', 'duration'],
        filters: { uuid: service_uuid },
    });
    // if service info is empty, send error message
    // @ts-ignore
    if (serviceInfo.length === 0) {
        return ctx.badRequest('Service Not Found', 'Event/Programme not found');
    }
    // if it's 2 hours after the service, send error message
    const serviceDate = new Date(serviceInfo[0].start_time);
    const currentDate = new Date();
    const diff = currentDate.getTime() - serviceDate.getTime();
    const diffHours = Math.ceil(diff / (1000 * 60 * 60));

    if (diffHours >= serviceInfo[0].duration) {
        return ctx.badRequest('Too Late', `Service started ${diffHours} hours ago and lasted for ${serviceInfo[0].duration} hours!`);
    }
    if (diffHours < -4) {
        return ctx.badRequest('Too Early', `Service starts in ${Math.abs(diffHours)} hours!`);
    }
    const attendanceInfo = await strapi.documents('api::attendance-sheet.attendance-sheet').findMany({
        fields: ['id'],
        filters: {
            programme_id: serviceInfo[0].uuid,
            member_id: memberInfo[0].id,
         },
    });
    // if attendance info is not empty, send error message
    // @ts-ignore
    if (attendanceInfo.length > 0) {
        return ctx.badRequest('Already Marked', 'Your attendance record is already taken.');
    }

    // create attendance
    const entry = await strapi.documents('api::attendance-sheet.attendance-sheet').create({
    data: {
        programme_name: serviceInfo[0].title,
        programme_id: serviceInfo[0].uuid,
        member_id: memberInfo[0].id,
        uuid: uuidv4(),
    },
    });

    ctx.body = {entry};
  },
  async getMeetingDetails(ctx) {
    let { uuid } = ctx.request.params;
    if( ! uuid) uuid = 'none';
    const serviceInfo = await strapi.documents('api::church-service.church-service').findMany({
        fields: ['id', 'title', 'start_time', 'duration'],
        filters: { uuid: uuid },
    });
    // @ts-ignore
    if (serviceInfo.length === 0) {
        return ctx.badRequest('Not Found', { message: 'Invalid Event/Programme Link' });
    }
    ctx.body = {
        ...serviceInfo[0],
    };
  }
};
