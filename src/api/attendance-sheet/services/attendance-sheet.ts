/**
 * attendance-sheet service
 */

import { factories } from '@strapi/strapi';
import superagent from 'superagent';

export default factories.createCoreService('api::attendance-sheet.attendance-sheet', ({ strapi }) => ({
    createQRCode: async (link: string) => {
        // call qr code api to generate qr code for link
        // see https://goqr.me/api/ for docs
        const response = await superagent.get('https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + link);    
        const qrCode = response.body;
        const buffer = Buffer.from(qrCode);
        const base64Image = buffer.toString('base64');
        const imgSrcString = `data:image/png;base64,${base64Image}`;
        return { 
            str: imgSrcString, 
            raw: qrCode
        };
    }
    }));
