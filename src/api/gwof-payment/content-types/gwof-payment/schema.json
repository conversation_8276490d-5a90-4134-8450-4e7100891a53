{"kind": "collectionType", "collectionName": "gwof_payments", "info": {"singularName": "gwof-payment", "pluralName": "gwof-payments", "displayName": "GWOF Payment"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"partner_id": {"type": "string"}, "payment_link": {"type": "string"}, "payment_date": {"type": "datetime"}, "currency": {"type": "string"}, "status": {"type": "string"}, "commitment": {"type": "string"}, "start": {"type": "date"}, "end": {"type": "date"}, "txn_ref": {"type": "string"}, "amount": {"type": "integer"}, "description": {"type": "string"}}}