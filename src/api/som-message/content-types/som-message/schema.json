{"kind": "collectionType", "collectionName": "som_messages", "info": {"singularName": "som-message", "pluralName": "som-messages", "displayName": "SOM Message", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"channel": {"type": "string"}, "status": {"type": "enumeration", "enum": ["success", "pending", "failed"], "default": "pending"}, "description": {"type": "string"}, "recipient_id": {"type": "integer"}}}