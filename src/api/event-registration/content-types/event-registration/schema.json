{"kind": "collectionType", "collectionName": "event_registrations", "info": {"singularName": "event-registration", "pluralName": "event-registrations", "displayName": "Event Registration"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"event": {"type": "relation", "relation": "oneToOne", "target": "api::event.event"}, "event_attendee": {"type": "relation", "relation": "oneToMany", "target": "api::event-attendee.event-attendee"}}}