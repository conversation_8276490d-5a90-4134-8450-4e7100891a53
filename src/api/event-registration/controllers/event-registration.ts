/**
 * event-registration controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::event-registration.event-registration', ({ strapi }) => ({
  // Override the default create method to add email sending
  async create(ctx) {
    console.log('=== POST /api/event-registrations called ===');
    console.log('Request body:', JSON.stringify(ctx.request.body, null, 2));

    // Call the default create method
    const response = await super.create(ctx);

    console.log('Event registration response:', response);

    return response;
  }
}));
