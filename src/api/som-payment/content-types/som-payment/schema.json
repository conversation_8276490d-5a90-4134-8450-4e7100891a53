{"kind": "collectionType", "collectionName": "som_payments", "info": {"singularName": "som-payment", "pluralName": "som-payments", "displayName": "SOM Payment", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"payer_id": {"type": "integer"}, "payer_type": {"type": "string", "default": "registration"}, "payment_status": {"type": "string", "default": "pending"}, "amount": {"type": "decimal", "default": 0}, "amount_paid": {"type": "decimal", "default": 0}, "due_at": {"type": "date"}, "currency": {"type": "string", "default": "NGN"}, "reference": {"type": "string"}, "description": {"type": "string"}}}