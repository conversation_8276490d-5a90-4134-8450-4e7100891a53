{"kind": "collectionType", "collectionName": "gwof_partnerships", "info": {"singularName": "gwof-partnership", "pluralName": "gwof-partnerships", "displayName": "GWOF Partnership", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"row_number": {"type": "integer"}, "spreadsheet_id": {"type": "string"}, "sheet": {"type": "string"}, "timestamp": {"type": "string"}, "full_name": {"type": "string"}, "email": {"type": "email"}, "phone": {"type": "string"}, "address": {"type": "string"}, "amount": {"type": "integer"}, "commitment": {"type": "string"}, "comments": {"type": "string"}, "currency": {"type": "enumeration", "enum": ["USD", "EUR", "GBP", "NGN"]}}}