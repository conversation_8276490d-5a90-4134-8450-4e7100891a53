{"kind": "collectionType", "collectionName": "converts", "info": {"singularName": "convert", "pluralName": "converts", "displayName": "Convert", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "address": {"type": "string"}, "gender": {"type": "enumeration", "enum": ["Male", "Female", "male", "female"]}, "marital_status": {"type": "enumeration", "enum": ["single", "married", "separated", "divorced"]}, "prayer_request": {"type": "text"}, "converted_by": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "converts"}, "ministry_materials": {"type": "string"}}}