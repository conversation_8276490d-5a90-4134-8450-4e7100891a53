{"kind": "collectionType", "collectionName": "partner_messages", "info": {"singularName": "partner-message", "pluralName": "partner-messages", "displayName": "Partner Message"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"channel": {"type": "string"}, "status": {"type": "enumeration", "enum": ["success", "pending", "failed"]}, "description": {"type": "string"}, "recipient_id": {"type": "integer"}, "message_type": {"type": "string", "default": "transaction created"}}}