{"kind": "collectionType", "collectionName": "petra_campuses", "info": {"singularName": "petra-campus", "pluralName": "petra-campuses", "displayName": "Website Petra Campus", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"location": {"type": "string"}, "address": {"type": "string"}, "resident_pastor": {"type": "string"}, "meeting_days": {"type": "string"}, "long": {"type": "float"}, "lat": {"type": "float"}, "leaders_bio": {"type": "text"}, "contact_email": {"type": "email"}, "contact_phone": {"type": "string"}, "resident_pastor_img": {"type": "string"}, "slug": {"type": "string"}, "instagram": {"type": "string"}, "map_img": {"type": "string"}, "campus_banner": {"type": "string"}, "order": {"type": "integer"}}}