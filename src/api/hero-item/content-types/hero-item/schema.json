{"kind": "collectionType", "collectionName": "hero_items", "info": {"singularName": "hero-item", "pluralName": "hero-items", "displayName": "Website Hero Item", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"Title": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "subtitle": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "icon": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "url": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "description": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "button_text": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "route": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "position": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "status": {"pluginOptions": {"i18n": {"localized": true}}, "type": "enumeration", "enum": ["active", "inactive"]}, "banner": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "ministers": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}}}