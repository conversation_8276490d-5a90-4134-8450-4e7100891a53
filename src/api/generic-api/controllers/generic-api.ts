// @ts-nocheck
/**
 * A set of functions called "actions" for `international-delegate-api`
 */

export default {
  registerInternationalDelegate: async (ctx, next) => {
    try {
      // validate the form
      const { email, firstname, surname, phone, country } = ctx.request.body;
      if (! email || ! firstname || ! surname || ! phone || ! country) {
        ctx.body = {
          message: 'All fields are required',
          ok: false,
        };
        ctx.status = 400;
        return;
      }
      let res = await strapi.service('api::generic-api.generic-api').registerInternationalDelegate(ctx.request.body);

      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }

      let fd = res.foreignDelegate;
      ctx.body = {
        fd,
        message: res.message
      };
     } catch (err) {
       console.log(err);
       ctx.body = err;
     }
  },
  getUpcomingProgramme: async (ctx, next) => {
    let res = await strapi.service('api::generic-api.generic-api').getUpcomingProgramme();
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = res;
  },

  createAnnouncement: async (ctx, next) => {
    try {
      const { title, details, flyer, flyer_url } = ctx.request.body;
      if (! title || ! details || ! flyer_url ) {
        ctx.body = {
          message: 'These fields are required',
          ok: false,
        };
        ctx.status = 400;
        return;
      }
      let res = await strapi.service('api::generic-api.generic-api').createAnnouncement(ctx.request.body);

      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }

      let announcement = res.Announcement;
      ctx.body = {
        announcement,
        message: 'New Announcement Created'
      };

    } catch (err) {
      console.log(err);
      ctx.body = err;
    }
  },

  getOneAnnouncement: async (ctx) => {
    try {
      const { id } = ctx.params;
      const announcement = await strapi.service('api::generic-api.generic-api').getOneAnnouncement(id);
      if (!announcement) {
        ctx.status = 404;
        ctx.body = { error: 'Announcement not found' };
        return;
      }
      ctx.body = announcement;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },

  getAllAnnouncements: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').getAllAnnouncements();
      if (res.ok) {
        ctx.body = res.data;
      } else {
        ctx.body = res.error;
        ctx.status = 500;
      }
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },

  getInternationalDelegate: async (ctx) => {
    try {
      const { id } = ctx.params;
      const announcement = await strapi.service('api::generic-api.generic-api').getInternationalDelegate(id);
      if (!announcement) {
        ctx.status = 404;
        ctx.body = { error: 'International delegate not found' };
        return;
      }
      ctx.body = announcement;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },

  getAllInternationalDelegate: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').getAllInternationalDelegate();
      if (res.ok) {
        ctx.body = res.data;
      } else {
        ctx.body = res.error;
        ctx.status = 500;
      }
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  registerEventAttendance: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').registerEventAttendance(ctx.request.body);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      const data = ctx.request.body;

      // Send general registration email to all attendees
      await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(data.data.event, data.data.event_attendee);

      // Also send minister-specific email if attendee is a minister
      await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(data.data.event, data.data.event_attendee);

      ctx.body = res;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  markMinisterAttendance: async (ctx) => {
    try {
      const { event_id, attendee_id } = ctx.request.params;

      const res = await strapi.service('api::generic-api.generic-api').markMinisterAttendance(event_id, attendee_id);
      if (res.ok) {
        ctx.redirect(`${process.env.FRONTEND_URL}/ministers/registrationsuccess?message=${res.message}&minister=${res.attendee.name}&ministry_name=${res.attendee.ministry_name}&ministry_position=${res.attendee.ministry_position}&ministry_location=${res.attendee.ministry_location}`);
        return;
      } else {
        ctx.redirect(`${process.env.FRONTEND_URL}/ministers/registrationerror?message=${res.message}`);
        return;
      }
    } catch (err) {
      ctx.body = `Something went wrong: ${err.message}`;
      ctx.status = 500;
    }
  },
  runEmailsForMinisters: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').runEmailsForMinisters();
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = res;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  runEmailsForEventAttendees: async (ctx) => {
    try {
      const { event_name } = ctx.request.query;
      const res = await strapi.service('api::generic-api.generic-api').runEmailsForEventAttendees(event_name);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = res;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  logGwofPartnership: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').logGwofPartnership(ctx.request.body);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = res;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  logTSCReg: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').logTSCReg(ctx.request.body);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = res;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  runEmailsForTSC: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').runEmailsForTSC();
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = res;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  markTSCAttendance: async (ctx) => {
    const { event_id, attendee_id } = ctx.request.params;
    const is_api = ctx.request.query.is_api;
    try {
      const res = await strapi.service('api::generic-api.generic-api').markTSCAttendance(event_id, attendee_id);
      if (res.ok) {
        if (is_api) {
          ctx.body = res;
          return;
        }
        const redirect_url = `${process.env.FRONTEND_URL}/tsc-reg-confirmation/registrationsuccess?message=${res.message}&attendee=${res.registration.full_name}&email=${res.registration.email}&expectations=${res.registration.expectations}`;
        ctx.redirect(redirect_url);
        return;
      }
      if (is_api) {
          ctx.body = res;
          return;
      }
      ctx.redirect(`${process.env.FRONTEND_URL}/tsc-reg-confirmation/registrationerror?message=${res.message}`);
    } catch (err) {
      if (is_api) {
          ctx.body = err;
          return;
      }
      ctx.redirect(`${process.env.FRONTEND_URL}/tsc-reg-confirmation/registrationerror?message=${err.message}`);
    }
  },
  markTSCAttendanceManual: async (ctx) => {
    const { event_id, email, phone } = ctx.request.query;
    const is_api = ctx.request.query.is_api;
    try {
      const res = await strapi.service('api::generic-api.generic-api').markTSCAttendanceManual(event_id, email, phone);
      if (res.ok) {
        if (is_api) {
          ctx.body = res;
          return;
        }
        const redirect_url = `${process.env.FRONTEND_URL}/tsc-reg-confirmation/registrationsuccess?message=${res.message}&attendee=${res.registration.full_name}&email=${res.registration.email}&expectations=${res.registration.expectations}`;
        ctx.redirect(redirect_url);
        return;
      }
      if (is_api) {
        ctx.body = res;
        return;
      }
      ctx.redirect(`${process.env.FRONTEND_URL}/tsc-reg-confirmation/registrationerror?message=${res.message}`);
    } catch (err) {
      if (is_api) {
        ctx.body = err;
        return;
      }
      ctx.redirect(`${process.env.FRONTEND_URL}/tsc-reg-confirmation/registrationerror?message=${err.message}`);
    }
  },

  markEventAttendance: async (ctx) => {
    try {
      const { event_id, attendee_id } = ctx.request.params;

      const res = await strapi.service('api::generic-api.generic-api').markEventAttendance(event_id, attendee_id);
      if (res.ok) {
        ctx.redirect(`${process.env.FRONTEND_URL}/event/registrationsuccess?message=${res.message}&attendee=${res.attendee.name}&event_id=${event_id}`);
        return;
      } else {
        ctx.redirect(`${process.env.FRONTEND_URL}/event/registrationerror?message=${res.message}`);
        return;
      }
    } catch (err) {
      ctx.body = `Something went wrong: ${err.message}`;
      ctx.status = 500;
    }
  },

  getCampusAccountDetails: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').getCampusAccountDetails();
      if (res.ok) {
        ctx.body = res;
      } else {
        ctx.body = res;
        ctx.status = 500;
      }
    } catch (err) {
      ctx.body = { ok: false, error: err.message };
      ctx.status = 500;
    }
  }
};
