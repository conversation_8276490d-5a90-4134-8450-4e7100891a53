// @ts-nocheck
const FIELDS = ['firstname', 'surname', 'address', 'city', 'state', 'postal_code', 'country', 'phone', 'email', 'organisation', 'organisation_type'];

const campusBankDetails = [
  {
    id: "global",
    name: "Tribe Petra Global",
    accountName: "Tribe Petra World Ministry",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "ike<PERSON>",
    name: "Petra Ikeja",
    accountName: "Tribe Petra Ikeja",
    bank: "Guaranty Trust Bank",
    accountNumber: "0********9",
    currency: "NGN",
  },
  {
    id: "lekki",
    name: "<PERSON>",
    accountName: "Tribe Petra Lekki",
    bank: "Guaranty Trust Bank",
    accountNumber: "09********",
    currency: "NGN",
  },
  {
    id: "wuse",
    name: "Petra Wuse",
    accountName: "Tribe Petra Wuse",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "apo",
    name: "Petra Apo",
    accountName: "Tribe Petra Apo",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "kubwa",
    name: "Petra Kubwa",
    accountName: "Tribe Petra Kubwa",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "maraba",
    name: "Petra Maraba",
    accountName: "Tribe Petra Maraba",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "london",
    name: "Petra London",
    accountName: "Tribe Petra London",
    bank: "Barclays Bank",
    accountNumber: "********",
    sortCode: "20-12-34",
    currency: "GBP",
  },
  {
    id: "manchester",
    name: "Petra Manchester",
    accountName: "Tribe Petra Manchester",
    bank: "Barclays Bank",
    accountNumber: "********",
    sortCode: "20-43-21",
    currency: "GBP",
  },
  {
    id: "port-harcourt",
    name: "Petra Port Harcourt",
    accountName: "Tribe Petra Port Harcourt",
    bank: "Guaranty Trust Bank",
    accountNumber: "9********0",
    currency: "NGN",
  },
  {
    id: "ibadan",
    name: "Petra Ibadan",
    accountName: "Tribe Petra Ibadan",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "accra",
    name: "Petra Accra",
    accountName: "Tribe Petra Accra",
    bank: "Ecobank Ghana",
    accountNumber: "**********",
    currency: "GHS",
  },
  {
    id: "canada",
    name: "Petra Canada",
    accountName: "Tribe Petra Canada",
    bank: "Royal Bank of Canada",
    accountNumber: "************",
    transitNumber: "12345",
    institutionNumber: "003",
    currency: "CAD",
  },
];

/**
 * international-delegate-api service
 */
export default () => ({
  registerInternationalDelegate: async (data) => {
    const event = data.event || "Rain Conference 2024";

    // Add validation for country
    if (data.country.toLowerCase() === 'nigeria') {
      return {
        message: 'This registration is for international delegates only. Nigerian delegates should use the local registration form.',
        ok: false,
      };
    }

    const fd = await strapi.documents(`api::international-delegate.international-delegate`).findMany({
        fields: FIELDS,
        filters: {
            email: data.email,
            event: event,
        },
    });
    if (fd.length > 0) {
      return {
        message: 'You have already registered for this event',
        ok: false,
      };
    }

    const newFD = await strapi.documents(`api::international-delegate.international-delegate`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Delegate record saved successfully',
        ok: true,
        foreignDelegate: newFD,
    };
  },
  getUpcomingProgramme: async () => {
    const up = await strapi.documents(`api::upcoming-programme.upcoming-programme`).findMany({});
    return {
      ok: true,
      data: up
    }
  },
  createAnnouncement: async (data) => {
    const newAnnouncement = await strapi.documents(`api::announcement.announcement`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Announcement created successfully',
        ok: true,
        Announcement: newAnnouncement,
    };
  },
  getOneAnnouncement: async (id) => {
    const announcements = await strapi.documents('api::announcement.announcement').findMany({
      filters: { id: id }, // Filter by ID
      populate: ['flyer'] // Populate the flyer field if needed
    });
    const announcement = announcements.length > 0 ? announcements[0] : null; // Extract the first element if found
    return announcement;
  },
  getAllAnnouncements: async () => {
    try {
      const announcements = await strapi.documents('api::announcement.announcement').findMany({
        sort: { createdAt: 'desc' }, // Sort by createdAt in descending order (newest first)
      });

      if (!announcements || announcements.length === 0) {
        return {
          ok: true,
          data: [],
          message: 'No announcements found',
        };
      }

      return {
        ok: true,
        data: announcements,
      };
    } catch (error) {
      console.error('Error fetching announcements:', error);
      return {
        ok: false,
        error: 'Failed to fetch announcements',
      };
    }
  },
  getInternationalDelegate: async (id) => {
    const internationalDelegates = await strapi.documents('api::international-delegate.international-delegate').findMany({
      filters: { id: id }
    });
    const internationalDelegate = internationalDelegates.length > 0 ? internationalDelegates[0] : null;
    return {
      ok: true,
      message: 'successful',
      internationalDelegate // Return the found delegate or null
    };
  },
  getAllInternationalDelegate: async () => {
    try {
      const internationalDelegates = await strapi.documents('api::international-delegate.international-delegate').findMany({
      });

      if (!internationalDelegates || internationalDelegates.length === 0) {
        return {
          ok: true,
          data: [],
          message: 'No international delegates found',
        };
      }

      return {
        ok: true,
        data: internationalDelegates,
      };
    } catch (error) {
      console.error('Error international delegates:', error);
      return {
        ok: false,
        error: 'Failed to fetch international delegates',
      };
    }
  },
  registerEventAttendance: async (data) => {
    console.log('=== registerEventAttendance service called ===');
    console.log('Input data:', JSON.stringify(data, null, 2));

    const attendance = data.data;
    console.log('Attendance data:', JSON.stringify(attendance, null, 2));

    console.log('Checking for existing registration...');
    const existing = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
        fields: ['id'],
        filters: {
            event_session: attendance.event_session,
            event: attendance.event,
            event_attendee: attendance.event_attendee,
        },
    });
    console.log('Existing registrations found:', existing.length);

    if (existing.length > 0) {
      console.log('Registration already exists, but checking if emails were sent...');

      // Still try to send emails for existing registrations if they haven't been sent
      try {
        console.log('Starting email sending process for existing registration...');
        console.log('Event ID:', attendance.event);
        console.log('Attendee ID(s):', attendance.event_attendee);

        // Handle event_attendee as array or single value
        const attendeeIds = Array.isArray(attendance.event_attendee) ? attendance.event_attendee : [attendance.event_attendee];
        console.log('Processing attendee IDs:', attendeeIds);

        // Send emails to each attendee
        for (const attendeeId of attendeeIds) {
          console.log('Processing attendee ID:', attendeeId);

          // Send general registration email to all attendees
          console.log('Calling sendEventRegistrationEmail for attendee:', attendeeId);
          const generalEmailResult = await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(attendance.event, attendeeId);
          console.log('General registration email result:', generalEmailResult);

          // Also send minister-specific email if attendee is a minister
          console.log('Calling sendEmailIfMinister for attendee:', attendeeId);
          const ministerEmailResult = await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(attendance.event, attendeeId);
          console.log('Minister email result:', ministerEmailResult);
        }
      } catch (emailError) {
        console.error('Error sending registration emails for existing registration:', emailError);
        console.error('Error stack:', emailError.stack);
        // Don't fail the registration if email fails, just log the error
      }

      return {
        message: 'You have already registered for this event',
        ok: true,
        attendance: existing[0],
      };
    }

    console.log('Creating new registration record...');
    let new_record;
    try {
      new_record = await strapi.documents(`api::event-attendance.event-attendance`).create({
          data: {
              ...attendance,
          }
      });
      console.log('New record created successfully:', new_record.id);
    } catch (createError) {
      console.error('Error creating registration record:', createError);
      throw createError;
    }

    // Send emails immediately upon successful registration
    try {
      console.log('Starting email sending process...');
      console.log('Event ID:', attendance.event);
      console.log('Attendee ID(s):', attendance.event_attendee);

      // Handle event_attendee as array or single value
      const attendeeIds = Array.isArray(attendance.event_attendee) ? attendance.event_attendee : [attendance.event_attendee];
      console.log('Processing attendee IDs:', attendeeIds);

      // Send emails to each attendee
      for (const attendeeId of attendeeIds) {
        console.log('Processing attendee ID:', attendeeId);

        // Send general registration email to all attendees
        console.log('Calling sendEventRegistrationEmail for attendee:', attendeeId);
        const generalEmailResult = await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(attendance.event, attendeeId);
        console.log('General registration email result:', generalEmailResult);

        // Also send minister-specific email if attendee is a minister
        console.log('Calling sendEmailIfMinister for attendee:', attendeeId);
        const ministerEmailResult = await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(attendance.event, attendeeId);
        console.log('Minister email result:', ministerEmailResult);
      }
    } catch (emailError) {
      console.error('Error sending registration emails:', emailError);
      console.error('Error stack:', emailError.stack);
      // Don't fail the registration if email fails, just log the error
    }

    return {
        message: 'Registration successful',
        ok: true,
        attendance: new_record,
    };
  },
  sendEmailIfMinister: async (event_id, attendee_id) => {
    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });
    const attendee = attendees.length > 0 ? attendees[0] : null;

    if(attendee && attendee.is_minister) {
      const emails_sent = await strapi.documents(`api::minister-welcome-email.minister-welcome-email`).findMany({
        filters: {
          $and: [
            { attendee: attendee_id },
            { event: event_id },
            { email_sent: true }
          ],
        },
      })
      if(emails_sent.length === 0) {
        const events = await strapi.documents(`api::event.event`).findMany({
              filters: { id: event_id },
            });
        const event = events.length > 0 ? events[0] : null;
        if(! event.title.startsWith('Rain Conference')) {
          return {
            ok: true,
            message: 'Non-Rain Conference/Immersion event detected',
            status: false
          }
        }

        const link = encodeURI(`https://api.petracc.org/api/generic-api/ministers/${event_id}/${attendee_id}`);
        const email = await strapi.service('api::office-api.emails').sendMinisterWelcomeEmail(attendee, event, link);
        await strapi.documents(`api::minister-welcome-email.minister-welcome-email`).create({
          data: {
            attendee: attendee_id,
            event: event_id,
            email_sent: email.response.status == 200
          }
        })
        return {
          ok: true,
          message: 'Welcome email sent successfully',
          status: email.response.status == 200
        }
      }
    }
    return {
      ok: true,
      message: 'Email not sent: Attendee not found or not a minister',
      status: false
    }
  },
  sendEventRegistrationEmail: async (event_id, attendee_id) => {
    console.log('Sending registration email for event:', event_id, 'attendee:', attendee_id);

    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });
    const attendee = attendees.length > 0 ? attendees[0] : null;

    console.log('Found attendee:', attendee ? 'Yes' : 'No');
    if (attendee) {
      console.log('Attendee data keys:', Object.keys(attendee));
      console.log('Attendee name:', attendee.name);
      console.log('Attendee email:', attendee.email);
    }

    if(attendee) {
      // Validate attendee has required fields
      if (!attendee.email) {
        return {
          ok: false,
          message: 'Attendee missing email field',
          status: false
        }
      }

      // Ensure name field exists, use email as fallback
      if (!attendee.name) {
        attendee.name = attendee.email.split('@')[0]; // Use email prefix as name fallback
      }

      // Check if email has already been sent
      const emails_sent = await strapi.documents(`api::event-welcome-email.event-welcome-email`).findMany({
        filters: {
          $and: [
            { attendee: attendee_id },
            { event: event_id },
            { email_sent: true }
          ],
        },
      })

      if(emails_sent.length === 0) {
        console.log('Looking up event with ID:', event_id);

        // Use documentId for more reliable lookups in Strapi v5
        console.log('Looking up event by documentId...');
        let events = [];

        try {
          // First try to find by documentId (most reliable)
          const eventByDocId = await strapi.documents(`api::event.event`).findOne({
            documentId: event_id
          });
          if (eventByDocId) {
            events = [eventByDocId];
            console.log('Event found by documentId:', eventByDocId.title);
          }
        } catch (docIdError) {
          console.log('DocumentId lookup failed, trying by id:', docIdError.message);

          // Fallback to id lookup
          try {
            const eventsByIds = await strapi.documents(`api::event.event`).findMany({
              filters: { id: event_id }
            });
            events = eventsByIds || [];
            console.log('Events found by id:', events.length);
          } catch (idError) {
            console.log('ID lookup also failed:', idError.message);
          }
        }

        if (events.length > 0) {
          console.log('Event found:', events[0].title);
        } else {
          console.log('No event found with ID:', event_id);
        }

        const event = events.length > 0 ? events[0] : null;

        if(!event) {
          console.log('Event not found with ID:', event_id);
          return {
            ok: false,
            message: 'Event not found',
            status: false
          }
        }

        // Create QR code link for general event attendance
        const link = encodeURI(`https://api.petracc.org/api/generic-api/event-attendance/${event_id}/${attendee_id}`);
        const email = await strapi.service('api::office-api.emails').sendEventRegistrationEmail(attendee, event, link);

        // Track email sending
        try {
          console.log('Creating email tracking record...');
          const emailTrackingRecord = await strapi.documents(`api::event-welcome-email.event-welcome-email`).create({
            data: {
              attendee: attendee_id,
              event: event_id,
              email_sent: email.response.status == 200
            }
          });
          console.log('Email tracking record created:', emailTrackingRecord.id);
        } catch (trackingError) {
          console.error('Error creating email tracking record:', trackingError);
          // Continue even if tracking fails
        }

        return {
          ok: true,
          message: 'Registration email sent successfully',
          status: email.response.status == 200
        }
      } else {
        return {
          ok: true,
          message: 'Registration email already sent',
          status: true
        }
      }
    }
    return {
      ok: false,
      message: 'Attendee not found',
      status: false
    }
  },
  markMinisterAttendance: async (event_id, attendee_id) => {
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: { id: event_id },
      populate: ['current_session']
    });
    const event = events.length > 0 ? events[0] : null;

    if (!event) {
      return { ok: false, message: 'Event not found' };
    }

    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });

    const attendee = attendees.length > 0 ? attendees[0] : null;

     if(!attendee) {
       return { ok: false, message: 'Attendee not found'};
     }
    const current_session = event.current_session.id;
    const existing = await strapi.documents(`api::minister-attendance.minister-attendance`).findMany({
      filters: {
        event: event_id,
        attendee: attendee_id,
        session: current_session,
      },
    });
    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked for this session',
        record: existing[0],
        attendee
      };
    }
    const record = await strapi.documents(`api::minister-attendance.minister-attendance`).create({
      data: {
        event: event_id,
        attendee: attendee_id,
        session: current_session,
      }
    })
    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      attendee
    };
  },
  runEmailsForMinisters: async () => {
    const event = await strapi.documents(`api::event.event`).findMany({
      filters: {
        name: 'Rain Conference 2025',
      },
    });
    const rc = event[0];
    const ministers = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: {
        is_minister: true,
      },
    });
    const attendance = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
      filters: {
        event: { id: rc.id},
      },
      populate: ['event_attendee']
    });
    const filtered_ministers = ministers.filter(minister => {
      return attendance.some(attendee => attendee.event_attendee[0].id === minister.id);
    });

    let sent_count = 0;
    for (const minister of filtered_ministers) {
      const email = await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(rc.id, minister.id);
      if(email.status) {
        sent_count++;
      }
    }

    return {
        message: `Successfully sent ${sent_count} out of ${filtered_ministers.length} emails`,
        ok: true,
    };
  },
  runEmailsForEventAttendees: async (event_name = 'Rain Conference 2025') => {
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: {
        title: event_name,
      },
    });

    if (!events || events.length === 0) {
      return {
        message: 'Event not found',
        ok: false,
      };
    }

    const event = events[0];
    const attendance = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
      filters: {
        event: { id: event.id},
      },
      populate: ['event_attendee']
    });

    let sent_count = 0;
    let skipped_count = 0;

    for (const attendanceRecord of attendance) {
      if (attendanceRecord.event_attendee && attendanceRecord.event_attendee.length > 0) {
        for (const attendee of attendanceRecord.event_attendee) {
          try {
            // Check if email was already sent to avoid duplicates
            const existingEmails = await strapi.documents(`api::event-welcome-email.event-welcome-email`).findMany({
              filters: {
                $and: [
                  { attendee: attendee.id },
                  { event: event.id },
                  { email_sent: true }
                ],
              },
            });

            if (existingEmails.length > 0) {
              skipped_count++;
              console.log(`Skipping attendee ${attendee.id} - email already sent`);
              continue;
            }

            const email = await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(event.id, attendee.id);
            if(email.status) {
              sent_count++;
            }
          } catch (err) {
            console.log(`Error sending email to attendee ${attendee.id}:`, err);
          }
        }
      }
    }

    return {
        message: `Successfully sent ${sent_count} registration emails for ${event.title}. Skipped ${skipped_count} attendees who already received emails.`,
        ok: true,
        sent_count,
        skipped_count
    };
  },
  logGwofPartnership: async (data) => {
    const gwof_partner = await strapi.documents(`api::gwof-partnership.gwof-partnership`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Registration successful',
        ok: true,
        gwof_partner
    };
  },
  logTSCReg: async (data) => {
    const tsc_reg = await strapi.documents(`api::tsc-registration.tsc-registration`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Registration successful',
        ok: true,
        tsc_reg
    };
  },
  runEmailsForTSC: async () => {
    // This entire implementation is likely a temporary fix just for TSC 25 which is one day away, as the function is not generic enough to handle dynamic events. A more robust solution should be implemented in the future ahead of the next TSC or other event.
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: {
        title: 'The Singles Conference Abuja 2025',
      },
      select: ['id', 'title', 'documentId'],
    });
    const tsc25 = events[0];
    const tsc_registrations = await strapi.documents(`api::tsc-registration.tsc-registration`).findMany({
      filters: {
        qrcode_email_sent: false,
      },
      select: ['id', 'documentId', 'email', 'full_name'],
      limit: 100,
    });
    let sent_count = 0;
    for (const reg of tsc_registrations) {
      try {
        const email = await strapi.service('api::generic-api.generic-api').sendTSCRegistrationEmail(reg, tsc25);
      if(email.status) {
        sent_count++;
      }
      } catch (err) {
        console.log(err);
      }
    }
    return {
        message: `Successfully sent ${sent_count} out of ${tsc_registrations.length} emails`,
        ok: true,
    };
  },
  sendTSCRegistrationEmail: async (registration, event) => {
    const event_id = event.documentId;
    const attendee_id = registration.documentId;
    const link = encodeURI(`https://api.petracc.org/api/generic-api/tsc-attendance/${event_id}/${attendee_id}`);
    const email = await strapi.service('api::office-api.emails').sendTSCRegistrationEmail(registration, event, link);

    await strapi.documents(`api::tsc-registration.tsc-registration`).update({
      documentId: registration.documentId,
      data: {
        qrcode_email_sent: email.response.status == 200
      }
    });
    return {
      ok: true,
      message: 'Welcome email sent successfully',
      status: email.response.status == 200
    }
  },
  markTSCAttendance: async (event_id, attendee_id) => {
    const registration = await strapi.documents(`api::tsc-registration.tsc-registration`).findOne({
      documentId: attendee_id,
      fields: ['id', 'full_name', 'email', 'phone', 'expectations'],
    });

    if (!registration) {
      return {
        ok: false,
        message: 'Registration not found',
      };
    }
    const event = await strapi.documents(`api::event.event`).findOne({
      documentId: event_id,
      fields: ['id', 'title'],
    });

    if (!event) {
      return {
        ok: false,
        message: 'Event not found',
      };
    }
    const existing = await strapi.documents(`api::tsc-attendance.tsc-attendance`).findMany({
      filters: {
        event_id: event_id,
        attendee_id: attendee_id,
      },
    });
    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked',
        record: existing[0],
        registration
      };
    }
    const record = await strapi.documents(`api::tsc-attendance.tsc-attendance`).create({
      data: {
        event_id: event_id,
        attendee_id: attendee_id,
      }
    })
    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      registration
    };
  },
  markTSCAttendanceManual: async (event_id, email = null, phone = null) => {

    const registrations = await strapi.documents(`api::tsc-registration.tsc-registration`).findMany({
      filters: {
        $or: [
          {
            email: email,
          },
          {
            phone: phone,
          },
        ],
      },
      fields: ['id', 'full_name', 'email', 'phone', 'expectations'],
    });

    if (!registrations) {
      return {
        ok: false,
        message: 'Registration not found',
      };
    }
    const registration = registrations[0];
    const attendee_id = registration.documentId
    const event = await strapi.documents(`api::event.event`).findOne({
      documentId: event_id,
      fields: ['id', 'title'],
    });

    if (!event) {
      return {
        ok: false,
        message: 'Event not found',
      };
    }
    const existing = await strapi.documents(`api::tsc-attendance.tsc-attendance`).findMany({
      filters: {
        event_id: event_id,
        attendee_id: attendee_id,
      },
    });
    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked',
        record: existing[0],
        registration
      };
    }
    const record = await strapi.documents(`api::tsc-attendance.tsc-attendance`).create({
      data: {
        event_id: event_id,
        attendee_id: attendee_id,
      }
    })
    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      registration
    };
  },
  markEventAttendance: async (event_id, attendee_id) => {
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: { id: event_id },
      populate: ['current_session']
    });
    const event = events.length > 0 ? events[0] : null;

    if (!event) {
      return { ok: false, message: 'Event not found' };
    }

    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });

    const attendee = attendees.length > 0 ? attendees[0] : null;

     if(!attendee) {
       return { ok: false, message: 'Attendee not found'};
     }

    // For general event attendance, we'll use the event-attendance collection
    const existing = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
      filters: {
        event: event_id,
        event_attendee: attendee_id,
      },
    });

    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked for this event',
        record: existing[0],
        attendee
      };
    }

    const record = await strapi.documents(`api::event-attendance.event-attendance`).create({
      data: {
        event: event_id,
        event_attendee: [attendee_id], // This is a relation array
        event_session: event.current_session?.id || null,
      }
    })

    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      attendee
    };
  },
  getCampusAccountDetails: async () => {
    try {
      return {
        ok: true,
        data: campusBankDetails,
        message: 'Campus account details retrieved successfully'
      };
    } catch (error) {
      console.error('Error fetching campus account details:', error);
      return {
        ok: false,
        error: 'Failed to fetch campus account details'
      };
    }
  }
});
