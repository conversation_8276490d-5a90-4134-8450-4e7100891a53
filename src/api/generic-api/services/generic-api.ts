// @ts-nocheck
import fs from 'fs';
import path from 'path';

const FIELDS = ['firstname', 'surname', 'address', 'city', 'state', 'postal_code', 'country', 'phone', 'email', 'organisation', 'organisation_type'];

const campusBankDetails = [
  {
    id: "global",
    name: "Tribe Petra Global",
    accountName: "Tribe Petra World Ministry",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "ikeja",
    name: "Petra Ikeja",
    accountName: "Tribe Petra Ikeja",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "lekki",
    name: "<PERSON> Lekki",
    accountName: "Tribe Petra Lekki",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "wuse",
    name: "<PERSON> Wuse",
    accountName: "Tribe Petra Wuse",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "apo",
    name: "Petra Apo",
    accountName: "Tribe Petra Apo",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "kubwa",
    name: "Petra Kubwa",
    accountName: "Tribe Petra Kubwa",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "maraba",
    name: "Petra Maraba",
    accountName: "Tribe Petra Maraba",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "london",
    name: "Petra London",
    accountName: "Tribe Petra London",
    bank: "Barclays Bank",
    accountNumber: "********",
    sortCode: "20-12-34",
    currency: "GBP",
  },
  {
    id: "manchester",
    name: "Petra Manchester",
    accountName: "Tribe Petra Manchester",
    bank: "Barclays Bank",
    accountNumber: "********",
    sortCode: "20-43-21",
    currency: "GBP",
  },
  {
    id: "port-harcourt",
    name: "Petra Port Harcourt",
    accountName: "Tribe Petra Port Harcourt",
    bank: "Guaranty Trust Bank",
    accountNumber: "9********0",
    currency: "NGN",
  },
  {
    id: "ibadan",
    name: "Petra Ibadan",
    accountName: "Tribe Petra Ibadan",
    bank: "Guaranty Trust Bank",
    accountNumber: "**********",
    currency: "NGN",
  },
  {
    id: "accra",
    name: "Petra Accra",
    accountName: "Tribe Petra Accra",
    bank: "Ecobank Ghana",
    accountNumber: "**********",
    currency: "GHS",
  },
  {
    id: "canada",
    name: "Petra Canada",
    accountName: "Tribe Petra Canada",
    bank: "Royal Bank of Canada",
    accountNumber: "************",
    transitNumber: "12345",
    institutionNumber: "003",
    currency: "CAD",
  },
];

/**
 * international-delegate-api service
 */
export default () => ({
  registerInternationalDelegate: async (data) => {
    const event = data.event || "Rain Conference 2024";

    // Add validation for country
    if (data.country.toLowerCase() === 'nigeria') {
      return {
        message: 'This registration is for international delegates only. Nigerian delegates should use the local registration form.',
        ok: false,
      };
    }

    const fd = await strapi.documents(`api::international-delegate.international-delegate`).findMany({
        fields: FIELDS,
        filters: {
            email: data.email,
            event: event,
        },
    });
    if (fd.length > 0) {
      return {
        message: 'You have already registered for this event',
        ok: false,
      };
    }

    const newFD = await strapi.documents(`api::international-delegate.international-delegate`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Delegate record saved successfully',
        ok: true,
        foreignDelegate: newFD,
    };
  },
  getUpcomingProgramme: async () => {
    const up = await strapi.documents(`api::upcoming-programme.upcoming-programme`).findMany({});
    return {
      ok: true,
      data: up
    }
  },
  createAnnouncement: async (data) => {
    const newAnnouncement = await strapi.documents(`api::announcement.announcement`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Announcement created successfully',
        ok: true,
        Announcement: newAnnouncement,
    };
  },
  getOneAnnouncement: async (id) => {
    const announcements = await strapi.documents('api::announcement.announcement').findMany({
      filters: { id: id }, // Filter by ID
      populate: ['flyer'] // Populate the flyer field if needed
    });
    const announcement = announcements.length > 0 ? announcements[0] : null; // Extract the first element if found
    return announcement;
  },
  getAllAnnouncements: async () => {
    try {
      const announcements = await strapi.documents('api::announcement.announcement').findMany({
        sort: { createdAt: 'desc' }, // Sort by createdAt in descending order (newest first)
      });

      if (!announcements || announcements.length === 0) {
        return {
          ok: true,
          data: [],
          message: 'No announcements found',
        };
      }

      return {
        ok: true,
        data: announcements,
      };
    } catch (error) {
      console.error('Error fetching announcements:', error);
      return {
        ok: false,
        error: 'Failed to fetch announcements',
      };
    }
  },
  getInternationalDelegate: async (id) => {
    const internationalDelegates = await strapi.documents('api::international-delegate.international-delegate').findMany({
      filters: { id: id }
    });
    const internationalDelegate = internationalDelegates.length > 0 ? internationalDelegates[0] : null;
    return {
      ok: true,
      message: 'successful',
      internationalDelegate // Return the found delegate or null
    };
  },
  getAllInternationalDelegate: async () => {
    try {
      const internationalDelegates = await strapi.documents('api::international-delegate.international-delegate').findMany({
      });

      if (!internationalDelegates || internationalDelegates.length === 0) {
        return {
          ok: true,
          data: [],
          message: 'No international delegates found',
        };
      }

      return {
        ok: true,
        data: internationalDelegates,
      };
    } catch (error) {
      console.error('Error international delegates:', error);
      return {
        ok: false,
        error: 'Failed to fetch international delegates',
      };
    }
  },
  registerEventAttendance: async (data) => {
    const attendance = data.data;

    // First, resolve the event documentId to get the numeric ID
    let eventNumericId = attendance.event;
    try {
      if (typeof attendance.event === 'string' && attendance.event.length > 10) {
        // This looks like a documentId, convert to numeric ID
        const eventByDocId = await strapi.documents(`api::event.event`).findOne({
          documentId: attendance.event
        });
        if (eventByDocId) {
          eventNumericId = eventByDocId.id;
          console.log('Converted event documentId to numeric ID:', attendance.event, '->', eventNumericId);
        }
      }
    } catch (eventLookupError) {
      console.error('Error looking up event:', eventLookupError.message);
    }

    // Update attendance data to use numeric event ID
    const attendanceWithNumericId = {
      ...attendance,
      event: eventNumericId
    };

    let existing = [];
    try {
      existing = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
          fields: ['id'],
          filters: {
              event_session: attendanceWithNumericId.event_session,
              event: attendanceWithNumericId.event,
              event_attendee: attendanceWithNumericId.event_attendee,
          },
      });
    } catch (existingError) {
      console.error('Error checking existing registrations:', existingError.message);
      // Continue with registration if check fails
      existing = [];
    }

    if (existing.length > 0) {
      console.log('Registration already exists, but checking if emails were sent...');

      // Still try to send emails for existing registrations if they haven't been sent
      try {
        // Handle event_attendee as array or single value
        const attendeeIds = Array.isArray(attendanceWithNumericId.event_attendee) ? attendanceWithNumericId.event_attendee : [attendanceWithNumericId.event_attendee];

        // Send emails to each attendee
        for (const attendeeId of attendeeIds) {
          // Send general registration email to all attendees (use original documentId for email)
          const generalEmailResult = await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(attendance.event, attendeeId);

          // Also send minister-specific email if attendee is a minister (use numeric ID)
          const ministerEmailResult = await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(eventNumericId, attendeeId);

          // Log only if there are errors
          if (!generalEmailResult.ok) {
            console.error('Failed to send registration email to attendee:', attendeeId, generalEmailResult.message);
          }
          if (!ministerEmailResult.ok && ministerEmailResult.message !== 'Email not sent: Attendee not found or not a minister') {
            console.error('Failed to send minister email to attendee:', attendeeId, ministerEmailResult.message);
          }
        }
      } catch (emailError) {
        console.error('Error sending registration emails for existing registration:', emailError.message);
        // Don't fail the registration if email fails, just log the error
      }

      return {
        message: 'You have already registered for this event',
        ok: true,
        attendance: existing[0],
      };
    }

    let new_record;
    try {
      new_record = await strapi.documents(`api::event-attendance.event-attendance`).create({
          data: {
              ...attendanceWithNumericId,
          }
      });
    } catch (createError) {
      console.error('Error creating registration record:', createError.message);
      throw createError;
    }

    // Send emails immediately upon successful registration
    try {
      // Handle event_attendee as array or single value
      const attendeeIds = Array.isArray(attendanceWithNumericId.event_attendee) ? attendanceWithNumericId.event_attendee : [attendanceWithNumericId.event_attendee];

      // Send emails to each attendee
      for (const attendeeId of attendeeIds) {
        // Send general registration email to all attendees (use original documentId for email)
        const generalEmailResult = await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(attendance.event, attendeeId);

        // Also send minister-specific email if attendee is a minister (use numeric ID)
        const ministerEmailResult = await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(eventNumericId, attendeeId);

        // Log only if there are errors
        if (!generalEmailResult.ok) {
          console.error('Failed to send registration email to attendee:', attendeeId, generalEmailResult.message);
        }
        if (!ministerEmailResult.ok && ministerEmailResult.message !== 'Email not sent: Attendee not found or not a minister') {
          console.error('Failed to send minister email to attendee:', attendeeId, ministerEmailResult.message);
        }
      }
    } catch (emailError) {
      console.error('Error sending registration emails:', emailError.message);
      // Don't fail the registration if email fails, just log the error
    }

    return {
        message: 'Registration successful',
        ok: true,
        attendance: new_record,
    };
  },
  sendEmailIfMinister: async (event_id, attendee_id) => {
    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });
    const attendee = attendees.length > 0 ? attendees[0] : null;

    if(attendee && attendee.is_minister) {
      const emails_sent = await strapi.documents(`api::minister-welcome-email.minister-welcome-email`).findMany({
        filters: {
          $and: [
            { attendee: attendee_id },
            { event: event_id },
            { email_sent: true }
          ],
        },
      })
      if(emails_sent.length === 0) {
        const events = await strapi.documents(`api::event.event`).findMany({
              filters: { id: event_id },
            });
        const event = events.length > 0 ? events[0] : null;
        if(! event.title.startsWith('Rain Conference')) {
          return {
            ok: true,
            message: 'Non-Rain Conference/Immersion event detected',
            status: false
          }
        }

        const link = encodeURI(`https://api.petracc.org/api/generic-api/ministers/${event_id}/${attendee_id}`);
        const email = await strapi.service('api::office-api.emails').sendMinisterWelcomeEmail(attendee, event, link);
        await strapi.documents(`api::minister-welcome-email.minister-welcome-email`).create({
          data: {
            attendee: attendee_id,
            event: event_id,
            email_sent: email.response.status == 200
          }
        })
        return {
          ok: true,
          message: 'Welcome email sent successfully',
          status: email.response.status == 200
        }
      }
    }
    return {
      ok: true,
      message: 'Email not sent: Attendee not found or not a minister',
      status: false
    }
  },
  sendEventRegistrationEmail: async (event_id, attendee_id) => {
    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });
    const attendee = attendees.length > 0 ? attendees[0] : null;

    if(attendee) {
      // Validate attendee has required fields
      if (!attendee.email) {
        return {
          ok: false,
          message: 'Attendee missing email field',
          status: false
        }
      }

      // Ensure name field exists, use email as fallback
      if (!attendee.name) {
        attendee.name = attendee.email.split('@')[0];
      }

      // Skip email duplicate check for now since the schema is causing issues
      // We'll rely on Mailgun's duplicate detection or implement this later

      // Look up event
      let events = [];
      try {
        const eventByDocId = await strapi.documents(`api::event.event`).findOne({
          documentId: event_id
        });
        if (eventByDocId) {
          events = [eventByDocId];
        }
      } catch (docIdError) {
        // Fallback to id lookup
        try {
          const eventsByIds = await strapi.documents(`api::event.event`).findMany({
            filters: { id: event_id }
          });
          events = eventsByIds || [];
        } catch (idError) {
          console.error('Event lookup failed:', idError.message);
        }
      }

      const event = events.length > 0 ? events[0] : null;

      if(!event) {
        return {
          ok: false,
          message: 'Event not found',
          status: false
        }
      }

      // Create QR code link for general event attendance
      const link = encodeURI(`https://api.petracc.org/api/generic-api/event-attendance/${event_id}/${attendee_id}`);
      const email = await strapi.service('api::office-api.emails').sendEventRegistrationEmail(attendee, event, link);

      // Skip email tracking for now due to schema issues
      // TODO: Fix email tracking schema to handle documentId properly

      return {
        ok: email.ok,
        message: email.ok ? 'Registration email sent successfully' : 'Email sending failed',
        status: email.ok && email.response && email.response.status == 200
      }
    }
    return {
      ok: false,
      message: 'Attendee not found',
      status: false
    }
  },
  markMinisterAttendance: async (event_id, attendee_id) => {
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: { id: event_id },
      populate: ['current_session']
    });
    const event = events.length > 0 ? events[0] : null;

    if (!event) {
      return { ok: false, message: 'Event not found' };
    }

    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });

    const attendee = attendees.length > 0 ? attendees[0] : null;

     if(!attendee) {
       return { ok: false, message: 'Attendee not found'};
     }
    const current_session = event.current_session.id;
    const existing = await strapi.documents(`api::minister-attendance.minister-attendance`).findMany({
      filters: {
        event: event_id,
        attendee: attendee_id,
        session: current_session,
      },
    });
    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked for this session',
        record: existing[0],
        attendee
      };
    }
    const record = await strapi.documents(`api::minister-attendance.minister-attendance`).create({
      data: {
        event: event_id,
        attendee: attendee_id,
        session: current_session,
      }
    })
    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      attendee
    };
  },
  runEmailsForMinisters: async () => {
    const event = await strapi.documents(`api::event.event`).findMany({
      filters: {
        name: 'Rain Conference 2025',
      },
    });
    const rc = event[0];
    const ministers = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: {
        is_minister: true,
      },
    });
    const attendance = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
      filters: {
        event: { id: rc.id},
      },
      populate: ['event_attendee']
    });
    const filtered_ministers = ministers.filter(minister => {
      return attendance.some(attendee => attendee.event_attendee[0].id === minister.id);
    });

    let sent_count = 0;
    for (const minister of filtered_ministers) {
      const email = await strapi.service('api::generic-api.generic-api').sendEmailIfMinister(rc.id, minister.id);
      if(email.status) {
        sent_count++;
      }
    }

    return {
        message: `Successfully sent ${sent_count} out of ${filtered_ministers.length} emails`,
        ok: true,
    };
  },
  runEmailsForEventAttendees: async (event_name = 'Rain Conference 2025') => {
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: {
        title: event_name,
      },
    });

    if (!events || events.length === 0) {
      return {
        message: 'Event not found',
        ok: false,
      };
    }

    const event = events[0];
    const attendance = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
      filters: {
        event: { id: event.id},
      },
      populate: ['event_attendee']
    });

    let sent_count = 0;
    let skipped_count = 0;

    for (const attendanceRecord of attendance) {
      if (attendanceRecord.event_attendee && attendanceRecord.event_attendee.length > 0) {
        for (const attendee of attendanceRecord.event_attendee) {
          try {
            // Check if email was already sent to avoid duplicates
            const existingEmails = await strapi.documents(`api::event-welcome-email.event-welcome-email`).findMany({
              filters: {
                $and: [
                  { attendee: attendee.id },
                  { event: event.id },
                  { email_sent: true }
                ],
              },
            });

            if (existingEmails.length > 0) {
              skipped_count++;
              console.log(`Skipping attendee ${attendee.id} - email already sent`);
              continue;
            }

            const email = await strapi.service('api::generic-api.generic-api').sendEventRegistrationEmail(event.id, attendee.id);
            if(email.status) {
              sent_count++;
            }
          } catch (err) {
            console.log(`Error sending email to attendee ${attendee.id}:`, err);
          }
        }
      }
    }

    return {
        message: `Successfully sent ${sent_count} registration emails for ${event.title}. Skipped ${skipped_count} attendees who already received emails.`,
        ok: true,
        sent_count,
        skipped_count
    };
  },
  logGwofPartnership: async (data) => {
    const gwof_partner = await strapi.documents(`api::gwof-partnership.gwof-partnership`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Registration successful',
        ok: true,
        gwof_partner
    };
  },
  logTSCReg: async (data) => {
    const tsc_reg = await strapi.documents(`api::tsc-registration.tsc-registration`).create({
        data: {
            ...data,
        }
    });
    return {
        message: 'Registration successful',
        ok: true,
        tsc_reg
    };
  },
  runEmailsForTSC: async () => {
    // This entire implementation is likely a temporary fix just for TSC 25 which is one day away, as the function is not generic enough to handle dynamic events. A more robust solution should be implemented in the future ahead of the next TSC or other event.
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: {
        title: 'The Singles Conference Abuja 2025',
      },
      select: ['id', 'title', 'documentId'],
    });
    const tsc25 = events[0];
    const tsc_registrations = await strapi.documents(`api::tsc-registration.tsc-registration`).findMany({
      filters: {
        qrcode_email_sent: false,
      },
      select: ['id', 'documentId', 'email', 'full_name'],
      limit: 100,
    });
    let sent_count = 0;
    for (const reg of tsc_registrations) {
      try {
        const email = await strapi.service('api::generic-api.generic-api').sendTSCRegistrationEmail(reg, tsc25);
      if(email.status) {
        sent_count++;
      }
      } catch (err) {
        console.log(err);
      }
    }
    return {
        message: `Successfully sent ${sent_count} out of ${tsc_registrations.length} emails`,
        ok: true,
    };
  },
  sendTSCRegistrationEmail: async (registration, event) => {
    const event_id = event.documentId;
    const attendee_id = registration.documentId;
    const link = encodeURI(`https://api.petracc.org/api/generic-api/tsc-attendance/${event_id}/${attendee_id}`);
    const email = await strapi.service('api::office-api.emails').sendTSCRegistrationEmail(registration, event, link);

    await strapi.documents(`api::tsc-registration.tsc-registration`).update({
      documentId: registration.documentId,
      data: {
        qrcode_email_sent: email.response.status == 200
      }
    });
    return {
      ok: true,
      message: 'Welcome email sent successfully',
      status: email.response.status == 200
    }
  },
  markTSCAttendance: async (event_id, attendee_id) => {
    const registration = await strapi.documents(`api::tsc-registration.tsc-registration`).findOne({
      documentId: attendee_id,
      fields: ['id', 'full_name', 'email', 'phone', 'expectations'],
    });

    if (!registration) {
      return {
        ok: false,
        message: 'Registration not found',
      };
    }
    const event = await strapi.documents(`api::event.event`).findOne({
      documentId: event_id,
      fields: ['id', 'title'],
    });

    if (!event) {
      return {
        ok: false,
        message: 'Event not found',
      };
    }
    const existing = await strapi.documents(`api::tsc-attendance.tsc-attendance`).findMany({
      filters: {
        event_id: event_id,
        attendee_id: attendee_id,
      },
    });
    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked',
        record: existing[0],
        registration
      };
    }
    const record = await strapi.documents(`api::tsc-attendance.tsc-attendance`).create({
      data: {
        event_id: event_id,
        attendee_id: attendee_id,
      }
    })
    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      registration
    };
  },
  markTSCAttendanceManual: async (event_id, email = null, phone = null) => {

    const registrations = await strapi.documents(`api::tsc-registration.tsc-registration`).findMany({
      filters: {
        $or: [
          {
            email: email,
          },
          {
            phone: phone,
          },
        ],
      },
      fields: ['id', 'full_name', 'email', 'phone', 'expectations'],
    });

    if (!registrations) {
      return {
        ok: false,
        message: 'Registration not found',
      };
    }
    const registration = registrations[0];
    const attendee_id = registration.documentId
    const event = await strapi.documents(`api::event.event`).findOne({
      documentId: event_id,
      fields: ['id', 'title'],
    });

    if (!event) {
      return {
        ok: false,
        message: 'Event not found',
      };
    }
    const existing = await strapi.documents(`api::tsc-attendance.tsc-attendance`).findMany({
      filters: {
        event_id: event_id,
        attendee_id: attendee_id,
      },
    });
    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked',
        record: existing[0],
        registration
      };
    }
    const record = await strapi.documents(`api::tsc-attendance.tsc-attendance`).create({
      data: {
        event_id: event_id,
        attendee_id: attendee_id,
      }
    })
    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      registration
    };
  },
  markEventAttendance: async (event_id, attendee_id) => {
    const events = await strapi.documents(`api::event.event`).findMany({
      filters: { id: event_id },
      populate: ['current_session']
    });
    const event = events.length > 0 ? events[0] : null;

    if (!event) {
      return { ok: false, message: 'Event not found' };
    }

    const attendees = await strapi.documents(`api::event-attendee.event-attendee`).findMany({
      filters: { id: attendee_id }
    });

    const attendee = attendees.length > 0 ? attendees[0] : null;

     if(!attendee) {
       return { ok: false, message: 'Attendee not found'};
     }

    // For general event attendance, we'll use the event-attendance collection
    const existing = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
      filters: {
        event: event_id,
        event_attendee: attendee_id,
      },
    });

    if (existing.length > 0) {
      return {
        ok: true,
        message: 'Attendance already marked for this event',
        record: existing[0],
        attendee
      };
    }

    const record = await strapi.documents(`api::event-attendance.event-attendance`).create({
      data: {
        event: event_id,
        event_attendee: [attendee_id], // This is a relation array
        event_session: event.current_session?.id || null,
      }
    })

    return {
      ok: true,
      message: 'Attendance marked successfully',
      record,
      attendee
    };
  },
  getCampusAccountDetails: async () => {
    try {
      return {
        ok: true,
        data: campusBankDetails,
        message: 'Campus account details retrieved successfully'
      };
    } catch (error) {
      console.error('Error fetching campus account details:', error);
      return {
        ok: false,
        error: 'Failed to fetch campus account details'
      };
    }
  },
  sendGPMLCEmails: async () => {
    try {
      const csvPath = path.join(process.cwd(), 'gpmlc.csv'); // or 'gpmlc.csv'
      const emails = fs.readFileSync(csvPath, 'utf8');
      const rows = emails.split('\n').filter(row => row.trim().length > 0);

      let sent_count = 0;
      let failed_count = 0;
      let results = [];

      // Skip the header row
      for (const [i, row] of rows.entries()) {
        const cols = row.split(',').map(x => x && x.trim());
        const email = cols[5];
        const firstName = cols[3];
        const lastName = cols[4];
        if (!email || !firstName || !lastName) continue;

        const fullName = `${firstName} ${lastName}`;
        const title = 'Petra GPMLC 2025';

        // Personalised greeting
        const email_content = `
          <div style="font-family: Arial, sans-serif; font-size: 15px; color: #222;">
            <p style="margin-bottom: 0.5em;">Hello <strong>${fullName}</strong>,</p>
            <h2 style="margin-top: 0.5em; color: #2b4a9e;">Your Access to GPMLC 2025 – Join Us Live Online!</h2>
            <p>
              <strong>GPMLC 2025 begins TODAY</strong>, and we’re so excited to have you join us online for this life-changing conference!
            </p>
            <p>
              Whether you're tuning in from your home, office, or anywhere around the world, get ready for two dynamic days of powerful teachings, strategic impartations, and divine direction for your ministry and leadership journey.
            </p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;" />
            <p style="margin-bottom: 0.2em;"><strong>📍 Here are your access details:</strong></p>
            <ul style="margin-top: 0;">
              <li><strong>Zoom Link:</strong> <a href="https://us06web.zoom.us/j/86936241392?pwd=PYLosa1EXf3S8Rog5PeHx9kaA2adqJ.1" target="_blank">Join Meeting</a></li>
              <li><strong>Meeting ID:</strong> <em>(see event details)</em></li>
              <li><strong>Passcode:</strong> <em>(see event details)</em></li>
            </ul>
            <p>
              <strong>🕘 Start Time:</strong> 5:00 PM (WAT)<br/>
              <strong>📅 Dates:</strong> July 2–3, 2025
            </p>
            <p>
              <strong>💻 Tip:</strong> Please log in 10–15 minutes early to settle in and avoid missing anything!
            </p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;" />
            <p style="font-size: 13px; color: #888;">If you have any questions, reply to this email or contact the event organizers.</p>
          </div>
        `;
        const template_params = {};
        const template_text = null;

        try {
          const res = await strapi.service('api::office-api.emails').sendEmail(
            [email],
            title,
            email_content,
            template_params,
            null
          );
          if (res.ok) {
            sent_count++;
          } else {
            failed_count++;
          }
          results.push({ email, status: res.ok });
        } catch (err) {
          failed_count++;
          results.push({ email, status: false, error: err.message });
        }
      }

      return {
        ok: true,
        message: `Successfully sent ${sent_count} emails. Failed: ${failed_count}`,
        sent_count,
        failed_count,
        results
      };
    } catch (err) {
      console.log(err);
      return {
        ok: false,
        message: 'Failed to send emails',
        err
      };
    }
   }
});
