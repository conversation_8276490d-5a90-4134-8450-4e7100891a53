// @ts-nocheck
export default {
  routes: [
    {
     method: 'POST',
     path: '/generic-api/foreign-delegates/register',
     handler: 'generic-api.registerInternationalDelegate',
     config: {
      auth: false,
      policies: [],
      middlewares: [],
     },
    },
    {
      method: 'GET',
      path: '/generic-api/upcoming-programme',
      handler: 'generic-api.getUpcomingProgramme',
      config: {
       auth: false,
       policies: [],
       middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/announcements/create',
      handler: 'generic-api.createAnnouncement',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
       },
    },
    {
      method: 'GET',
      path: '/announcements/get/:id',
      handler: 'generic-api.getOneAnnouncement',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/getannouncements',
      handler: 'generic-api.getAllAnnouncements',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/foreign-delegates/get/:id',
      handler: 'generic-api.getInternationalDelegate',
      config: {
        policies: [],
        middlewares: ["api::generic-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/foreign-delegates/getall',
      handler: 'generic-api.getAllInternationalDelegate',
      config: {
        policies: [],
        middlewares: ["api::generic-api.is-admin"],
      },
    },
    {
      method: 'POST',
      path: '/generic-api/event-attendance',
      handler: 'generic-api.registerEventAttendance',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/ministers/:event_id/:attendee_id',
      handler: 'generic-api.markMinisterAttendance',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/run-emails-for-ministers',
      handler: 'generic-api.runEmailsForMinisters',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/generic-api/log-gwof-partnership',
      handler: 'generic-api.logGwofPartnership',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/generic-api/log-singles-conference-response',
      handler: 'generic-api.logTSCReg',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/run-emails-for-singles-conference',
      handler: 'generic-api.runEmailsForTSC',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/tsc-attendance/:event_id/:attendee_id',
      handler: 'generic-api.markTSCAttendance',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/tsc-attendance-manual',
      handler: 'generic-api.markTSCAttendanceManual',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/generic-api/get-campus-account-details',
      handler: 'generic-api.getCampusAccountDetails',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    }
  ],
};
