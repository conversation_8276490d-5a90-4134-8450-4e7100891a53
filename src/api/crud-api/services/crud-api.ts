// @ts-nocheck
/**
 * crud-api service
 */

export default () => ({
    all: async (entity, fields) => {
        const info = await strapi.documents(`api::${entity}.${entity}`).findMany({
            fields,
        });
        return info;
    },
    search: async (entity, field, value, fields) => {
        const info = await strapi.documents(`api::${entity}.${entity}`).findMany({
            fields,
            filters: {
                [field]: value,
              },
        });
        return info;
    },
    createRecord: async (entity, data, no_duplicates) => {
        if(no_duplicates && entity === 'event-attendance') {
            const existing = await strapi.documents(`api::${entity}.${entity}`).findMany({
                populate: ['event', 'event_attendee', 'event_session'],
                filters: {
                    $and: [
                        { event: { id: data.event }},
                        { event_attendee: { id: data.event_attendee }},
                        { event_session: { id: data.event_session }},
                      ],
                },
            });
            if(existing.length) {
                const entry = await strapi.documents(`api::${entity}.${entity}`).update({
                    documentId: existing[0].documentId,
                    data
                });
                return entry;
            }
        }
        const entry = await strapi.documents(`api::${entity}.${entity}`).create({
            data
        });

        return entry;
    },
    updateRecord: async (entity, data, id) => {
        const entry = await strapi.documents(`api::${entity}.${entity}`).update({
            documentId: id,
            data
        });
        //this.sendEmailIfMinister(entity, data.event, data.event_attendee);
        return entry;
    },
    deleteRecord: async (entity, id) => {
        return await strapi.documents(`api::${entity}.${entity}`).delete({
            documentId: id
        });
    },
    getDepartments: async () => {
        return [
            { title: "Next Steps", value: "Next Steps" },
            { title: "Prayer", value: "Prayer" },
            { title: "Photography", value: "Photography" },
            { title: "Media", value: "Media" },
            { title: "Video", value: "Video" },
            { title: "Light", value: "Light" },
            { title: "Sound", value: "Sound" },
            { title: "Choir (R&R)", value: "Choir" },
            { title: "Care and Welfare", value: "Care and Welfare" },
            { title: "Greeters", value: "Greeters" },
            { title: "Connect", value: "Connect" },
            { title: "Virtual Church", value: "Virtual Church" },
            { title: "Special Duties", value: "Special Duties" },
            { title: "Facility Management", value: "Facility Management" },
            { title: "Traffic and Security", value: "Traffic and Security" },
            { title: "Projection", value: "Projection" },
            { title: "Ushers", value: "Ushers" },
            { title: "Protocol", value: "Protocol" },
            { title: "TPK (Childrens Church)", value: "TPK (Childrens Church)" },
            { title: "IT", value: "IT" },
        ];
    },
    registerVBS: async (parent, children) => {
      let year = new Date().getFullYear();
      year = year.toString();
        let ids = [];
        for(let child of children) {
          // first check if the exact child exists with the year
          let existing = await strapi.documents(`api::tribe-petra-kid.tribe-petra-kid`).findMany({
            filters: {
              ...child,
              year: year,
            },
          });
          if(existing.length > 0) {
            ids.push(existing[0].id);
            continue;
          }
          const entry = await strapi.documents(`api::tribe-petra-kid.tribe-petra-kid`).create({
                data: {
                    ...child,
                    year: year
                }
            });
            ids.push(entry.id);
            // attach parent attributes to child object
            let full_child = {
                ...child,
                parent_name: `${parent.parent_first_name} ${parent.parent_last_name}`,
                parent_email: parent.parent_email,
                parent_phone: parent.parent_phone,
                petra_member: parent.petra_member,
                year: year
            };
            // send api call to Make webhook url to save data to google sheet
            // zapier webhook: https://hooks.zapier.com/hooks/catch/16126462/220obgk/
            // make webhook: https://hook.eu2.make.com/88wnmwx1soqun13ja1yg894q6b1wiu7g
            await fetch('https://hook.eu2.make.com/88wnmwx1soqun13ja1yg894q6b1wiu7g', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(full_child)
            })
        }
        // first check if the exact parent exists with the year
        let existing = await strapi.documents(`api::vbs-registration.vbs-registration`).findMany({
          filters: {
            ...parent,
            year: year,
            // children: ids,
          },
        });
        if(existing.length) {
          return existing[0];
        }
        const entry = await strapi.documents(`api::vbs-registration.vbs-registration`).create({
            data: {
                children: ids,
                ...parent,
                year: year
            }
        });

        return entry;
    },
});
