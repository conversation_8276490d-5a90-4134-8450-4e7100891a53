/**
 * A set of functions called "actions" for `crud-api`
 */

export default {
   all: async (ctx, next) => {
     try {
      let { entity } = ctx.request.params;
      let { fields } = ctx.request.query;
      let res = await strapi.service('api::crud-api.crud-api').all(entity, fields);
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
   },
   search: async (ctx, next) => {
    try {
      let { entity } = ctx.request.params;
     let { field, value, fields } = ctx.request.query;
     let res = await strapi.service('api::crud-api.crud-api').search(entity, field, value, fields);
     ctx.body = {data: res};
    } catch (err) {
      ctx.body = err;
    }
  },
  create: async (ctx, next) => {
    try {
     let { entity } = ctx.request.params;
     let { no_duplicates } = ctx.request.query;
     let res = await strapi.service('api::crud-api.crud-api').createRecord(entity, ctx.request.body, no_duplicates);
     ctx.body = {data: res};
    } catch (err) {
      ctx.body = err;
    }
  },
  update: async (ctx, next) => {
    try {
     let { id, entity } = ctx.request.params;
     let res = await strapi.service('api::crud-api.crud-api').updateRecord(entity, ctx.request.body, id);
     ctx.body = {data: res};
    } catch (err) {
      ctx.body = err;
    }
  },
  delete: async (ctx, next) => {
    try {
      let { id, entity } = ctx.request.params;
      let res = await strapi.service('api::crud-api.crud-api').deleteRecord(entity, id);
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  getDepartments: async (ctx, next) => {
    try {
      let res = await strapi.service('api::crud-api.crud-api').getDepartments();
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  registerVBS: async (ctx, next) => {
    try {
      let { parent, children } = ctx.request.body;
      let res = await strapi.service('api::crud-api.crud-api').registerVBS(parent, children);
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  }
};
