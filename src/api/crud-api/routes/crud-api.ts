export default {
  routes: [
    {
      method: 'GET',
      path: '/crud-api/get-departments',
      handler: 'crud-api.getDepartments',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/crud-api/register-vbs',
      handler: 'crud-api.registerVBS',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/crud-api/:entity',
      handler: 'crud-api.all',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/crud-api/search/:entity',
      handler: 'crud-api.search',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/crud-api/:entity',
      handler: 'crud-api.create',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/crud-api/:entity/:id',
      handler: 'crud-api.update',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'DELETE',
      path: '/crud-api',
      handler: 'crud-api.delete',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
