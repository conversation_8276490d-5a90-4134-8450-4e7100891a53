{"kind": "collectionType", "collectionName": "som_registrations", "info": {"singularName": "som-registration", "pluralName": "som-registrations", "displayName": "SOM Registration", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"surname": {"type": "string"}, "other_names": {"type": "string"}, "phone_number": {"type": "string"}, "email": {"type": "email", "unique": true}, "dob": {"type": "date"}, "address": {"type": "string"}, "occupation": {"type": "string"}, "marital_status": {"type": "string"}, "wedding_anniversary": {"type": "date"}, "no_of_children": {"type": "integer"}, "salvation_story": {"type": "text"}, "call": {"type": "text"}, "ministry_experience": {"type": "text"}, "joined_petra_at": {"type": "date"}, "joined_petra": {"type": "text"}, "who_is_pastor_ayo_to_you": {"type": "text"}, "commitment_to_petra": {"type": "text"}, "code": {"type": "string", "unique": true}, "is_petra_member": {"type": "enumeration", "enum": ["Yes", "No"]}, "photo_url": {"type": "string"}, "how_did_you_find_out": {"type": "text"}, "ministry": {"type": "string"}, "position_in_ministry": {"type": "string"}, "session": {"type": "string"}, "nationality": {"type": "string"}}}