/**
 * event-attendee controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::event-attendee.event-attendee', ({ strapi }) => ({
  // Override the default create method to add email sending
  async create(ctx) {
    console.log('=== POST /api/event-attendees called ===');
    console.log('Request body:', JSON.stringify(ctx.request.body, null, 2));

    // Call the default create method
    const response = await super.create(ctx);

    // If creation was successful, send emails
    if (response && response.data) {
      try {
        const attendeeId = response.data.id;
        console.log('New attendee created with ID:', attendeeId);

        // You might need to determine the event ID from the request or attendee data
        // For now, let's log what we have
        console.log('Attendee data:', response.data);

        // TODO: Add email sending logic here once we know the event ID
      } catch (emailError) {
        console.error('Error in post-creation email logic:', emailError);
      }
    }

    return response;
  }
}));
