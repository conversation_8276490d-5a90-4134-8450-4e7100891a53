{"kind": "collectionType", "collectionName": "event_attendees", "info": {"singularName": "event-attendee", "pluralName": "event-attendees", "displayName": "Event Attendee", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "phone": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "email": {"pluginOptions": {"i18n": {"localized": true}}, "type": "email"}, "gender": {"pluginOptions": {"i18n": {"localized": true}}, "type": "enumeration", "enum": ["Male", "Female"]}, "location": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "country": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "is_petra_member": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}, "is_petra_worker": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}, "is_minister": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}, "ministry_name": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "ministry_location": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "ministry_position": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "is_first_timer": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}, "petra_department": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "is_student": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}, "school_name": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "level_in_school": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "attendance_mode": {"pluginOptions": {"i18n": {"localized": true}}, "type": "enumeration", "enum": ["Online", "In-Person", "Hybrid"]}}}