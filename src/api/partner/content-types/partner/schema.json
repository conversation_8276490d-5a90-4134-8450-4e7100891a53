{"kind": "collectionType", "collectionName": "partners", "info": {"singularName": "partner", "pluralName": "partners", "displayName": "Partner", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"firstname": {"type": "string"}, "surname": {"type": "string"}, "partner_code": {"type": "string"}, "email": {"type": "email"}, "phone": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "currency": {"type": "enumeration", "enum": ["NGN", "USD", "GBP", "EUR", "GHS", "CAD"]}, "frequency": {"type": "enumeration", "enum": ["weekly", "monthly", "quarterly", "yearly"]}, "amount": {"type": "decimal"}}}