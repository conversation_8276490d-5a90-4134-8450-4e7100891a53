// @ts-nocheck
/**
 * emails service
 */
import Mailgun from "mailgun.js";
import formData from "form-data";

const mailgun = new Mailgun(formData);
const client = mailgun.client({ username: "process.env.MAILGUN_USER", key: process.env.MAILGUN_API_KEY });

import {
  TEMPLATE,
  TEMPLATE_MINISTER_WELCOME_TEXT,
  TEMPLATE_PARTNER_PASSWORD_REMINDER_TEXT,
  TEMPLATE_PARTNER_PAYMENT_ACKNOWLEDGEMENT_TEXT,
  TEMPLATE_PARTNER_PAYMENT_GENERATED_TEXT,
  TEMPLATE_SOM_UPDATE_EMAIL,
  TEMPLATE_TEXT,
  TEMPLATE_TSC_REGISTRATION,
  TEMPLATE_EVENT_REGISTRATION
} from "./email-templates"

export default () => ({
  sendAcceptanceEmail: async (documentId) => {
    const registration = await strapi.documents(`api::som-registration.som-registration`).findOne({
      documentId,
      fields: ['email', 'surname'],
    });

    if(! registration ) {
      return {
        ok: false,
        message: 'Registration not found',
      }
    }
    const id = registration.id;
    const messages = await strapi.documents(`api::som-message.som-message`).findMany({
      fields: ['status', 'channel', 'recipient_id'],
      filters: {
        recipient_id: id,
      },
    });

    if (messages.some(message => message.status === 'success')) {
      return {
        ok: false,
        message: 'Registration email already sent.',
      }
    }

    try {
      const { session, start_date, group_chat_link, venue } = await strapi.service('api::som-api.som-api').getCurrentSession();

      await client.messages.create(process.env.MAILGUN_DOMAIN, {
        from: `Ayo Ajani Int. Ministries <<EMAIL>>`, // sender address
        to: registration.email, // list of receivers
        subject: "Acceptance Letter - TMI Basic Ministry Course", // Subject line
        text: TEMPLATE_TEXT({ name: registration.surname, session, start_date, group_chat_link, venue }), // plain text body
        html: TEMPLATE({ name: registration.surname, session, start_date, group_chat_link, venue }), // html body
      });
      if(messages.length === 0) {
        await strapi.documents(`api::som-message.som-message`).create({
          data: {
            description: "Acceptance Letter",
            channel: "email",
            status: "success",
            recipient_id: registration.id,
          },
        });
      } else {
        await strapi.documents(`api::som-message.som-message`).update({
          documentId: messages[0].documentId,

          data: {
            status: "success",
          }
        });
      }
      return {
        ok: true,
        message: 'Email sent',
      }
    } catch (err) {
      console.log(err);
      if(messages.length === 0) {
        await strapi.documents(`api::som-message.som-message`).create({
          data: {
            description: "Acceptance Letter",
            channel: "email",
            status: "failed",
            recipient_id: registration.id,
          },
        });
      } else {
        await strapi.documents(`api::som-message.som-message`).update({
          documentId: messages[0].documentId,

          data: {
            status: "failed",
          }
        });
      }
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendPartnerPaymentAcknowledgementEmail: async (id) => {
    const partners = await strapi.documents(`api::partner-api.partner-api`).findMany({
          filters: { id: id },
          fields: ['email', 'surname', 'amount', 'date']
        });
    const partner = partners.length > 0 ? partners[0] : null;

    if(! partner ) {
      return {
        ok: false,
        message: 'Partner not found',
      }
    }
    const messages = await strapi.documents(`api::som-message.som-message`).findMany({
      fields: ['status', 'channel', 'recipient_id'],
      filters: {
        recipient_id: id,
      },
    });

    if (messages.some(message => message.status === 'success')) {
      return {
        ok: false,
        message: 'Partner payment acknowledgement email already sent.',
      }
    }

    try {
      await client.messages.create(process.env.MAILGUN_DOMAIN, {
        from: `Ayo Ajani Int. Ministries <<EMAIL>>`, // sender address
        to: registration.email, // list of receivers
        subject: "Partner Payment Acknowledgement", // Subject line
        text: TEMPLATE_PARTNER_PAYMENT_ACKNOWLEDGEMENT_TEXT({ name: partner.surname, amount: partner.amount, date: partner.date }), // plain text body
      });
      if(messages.length === 0) {
        await strapi.documents(`api::som-message.som-message`).create({
          data: {
            description: "Partner Payment Acknowledgement",
            channel: "email",
            status: "success",
            recipient_id: partner.id,
          },
        });
      } else {
        await strapi.documents(`api::som-message.som-message`).update({
          documentId: messages[0].documentId,

          data: {
            status: "success",
          }
        });
      }
      return {
        ok: true,
        message: 'Email sent',
      }
    } catch (err) {
      console.log(err);
      if(messages.length === 0) {
        await strapi.documents(`api::som-message.som-message`).create({
          data: {
            description: "Partner Payment Acknowledgement",
            channel: "email",
            status: "failed",
            recipient_id: partner.id,
          },
        });
      } else {
        await strapi.documents(`api::som-message.som-message`).update({
          documentId: messages[0].documentId,

          data: {
            status: "failed",
          }
        });
      }
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendTransactionGeneratedEmail: async (partner, start, end) => {
    const description = `Partner Payment Generated for period ${start} - ${end}`;
    const messages = await strapi.documents(`api::partner-message.partner-message`).findMany({
      fields: ['status', 'channel', 'recipient_id'],
      filters: {
        recipient_id: partner.id,
        description,
      },
    });

    if (messages.some(message => message.status === 'success')) {
      return {
        ok: false,
        message: 'Partner payment generated email already sent.',
      }
    }

    try {
      await client.messages.create(process.env.MAILGUN_DOMAIN, {
        from: `Ayo Ajani Int. Ministries <<EMAIL>>`,
        to: partner.email,
        subject: `${partner.frequency.toUpperCase()} Payment Transaction Generated`,
        text: TEMPLATE_PARTNER_PAYMENT_GENERATED_TEXT({ name: partner.firstname, amount: partner.amount, frequency: partner.frequency, start: start, end: end, currency: partner.currency, partner_code: partner.partner_code }),
      });
      if(messages.length === 0) {
        await strapi.documents(`api::partner-message.partner-message`).create({
          data: {
            description: description,
            channel: "email",
            status: "success",
            recipient_id: partner.id,
          },
        });
      } else {
        await strapi.documents(`api::partner-message.partner-message`).update({
          documentId: messages[0].documentId,

          data: {
            status: "success",
          }
        });
      }
      return {
        ok: true,
        message: 'Email sent',
      }
    } catch (err) {
      console.log(err);
      if(messages.length === 0) {
        await strapi.documents(`api::partner-message.partner-message`).create({
          data: {
            description: description,
            channel: "email",
            status: "failed",
            recipient_id: partner.id,
          },
        });
      } else {
        await strapi.documents(`api::partner-message.partner-message`).update({
          documentId: messages[0].documentId,

          data: {
            status: "failed",
          }
        });
      }
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendPasswordReminderEmail: async (partner) => {
    try {
      await client.messages.create(process.env.MAILGUN_DOMAIN, {
        from: `Ayo Ajani Int. Ministries <<EMAIL>>`,
        to: partner.email,
        subject: `Your Partner Portal Login Details`,
        text: TEMPLATE_PARTNER_PASSWORD_REMINDER_TEXT(partner),
      });

      return {
        ok: true,
        message: 'Email sent',
      }
    } catch (err) {
      console.log(err);
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendMinisterWelcomeEmail: async (attendee, event, link) => {
    try {
      const response = await client.messages.create(process.env.MAILGUN_DOMAIN, {
        from: `Ayo Ajani Int. Ministries <<EMAIL>>`,
        to: attendee.email,
        subject: `YOUR REGISTRATION CONFIRMATION FOR ${event.title}`,
        html: TEMPLATE_MINISTER_WELCOME_TEXT(attendee, event, link),
      });

      return {
        ok: true,
        message: 'Email sent',
        response
      }
    } catch (err) {
      console.log(err);
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendTSCRegistrationEmail: async (registration, event, link) => {
    try {
      const response = await client.messages.create(process.env.MAILGUN_DOMAIN, {
        from: `Petra Christian Centre Wuse <<EMAIL>>`,
        to: registration.email,
        subject: `YOUR REGISTRATION CONFIRMATION FOR ${event.title}`,
        html: TEMPLATE_TSC_REGISTRATION(registration, event, link),
      });

      return {
        ok: true,
        message: 'Email sent',
        response
      }
    } catch (err) {
      console.log(err);
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendEventRegistrationEmail: async (attendee, event, link) => {
    try {
      console.log('Preparing to send email to:', attendee.email);
      console.log('Event title:', event.title);
      console.log('Mailgun domain:', process.env.MAILGUN_DOMAIN);

      const emailData = {
        from: `Petra Christian Centre <<EMAIL>>`,
        to: attendee.email,
        subject: `YOUR REGISTRATION CONFIRMATION FOR ${event.title}`,
        html: TEMPLATE_EVENT_REGISTRATION(attendee, event, link),
      };

      console.log('Email data prepared:', {
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject
      });

      const response = await client.messages.create(process.env.MAILGUN_DOMAIN, emailData);
      console.log('Mailgun response:', response);

      return {
        ok: true,
        message: 'Email sent',
        response
      }
    } catch (err) {
      console.error('Email sending error:', err);
      return {
        ok: false,
        message: "Email sending failed, please try again another time",
        err
      };
    }
  },
  sendEmail: async (emails, title, template, template_params = {}, template_text = null) => {
    for (let email of emails) {
      try {
        const response = await client.messages.create(process.env.MAILGUN_DOMAIN, {
          from: `Ayo Ajani Int. Ministries <<EMAIL>>`,
          to: email,
          subject: title,
          text: template_text || template(template_params),
        });
      } catch(err) {
        console.log(err);
      }
    }
    return {
      ok: true,
      message: 'Emails sent',
      emails
    }
  }
});
