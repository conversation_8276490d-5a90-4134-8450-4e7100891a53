// @ts-nocheck
/**
 * office-api service
 */
import { TEMPLATE_SOM_UPDATE_EMAIL, TEMPLATE_SOM_INFO_EMAIL, TEMPLATE_SOM_INFO2_EMAIL } from "./email-templates";

// create an object of the two above templates with the templates as values
const GENERIC_EMAIL_TEMPLATES = {
  som_update_mail: TEMPLATE_SOM_UPDATE_EMAIL,
  som_info_mail: TEMPLATE_SOM_INFO_EMAIL,
  som_info2_mail: TEMPLATE_SOM_INFO2_EMAIL
};

const ATTENDEE_FIELDS = ['id', 'name', 'phone', 'email', 'gender', 'location', 'country', 'is_minister', 'ministry_name', 'ministry_location', 'ministry_position', 'attendance_mode'];
const SOM_FIELDS = ['code', 'surname', 'other_names', 'ministry', 'position_in_ministry', 'phone_number', 'email', 'dob', 'address', 'occupation', 'photo_url', 'marital_status', 'salvation_story', 'call', 'ministry_experience', 'who_is_pastor_ayo_to_you', 'is_petra_member', 'joined_petra_at', 'joined_petra', 'commitment_to_petra', 'how_did_you_find_out', 'wedding_anniversary', 'no_of_children'];
const PARTNER_FIELDS = ['id', 'firstname', 'surname', 'partner_code', 'email', 'phone', 'city', 'country', 'currency', 'frequency', 'amount'];

const dropDuplicates = (items, attribute) => {
  const uniqueKeys = new Set();
  return items.filter(obj => {
    const key = obj[attribute];
    if (!uniqueKeys.has(key)) {
      uniqueKeys.add(key);
      return true;
    }
    return false;
  });
}
export default () => ({
  getSpcRegistrations: async (eventTitle) => {
    // get all event attendances for event: eventTitle
    const event = await strapi.documents(`api::event.event`).findMany({
        fields: ['id', 'title'],
        filters: {
            title: eventTitle,
        },
    });
    const eventId = event[0].id;
    const eventAttendance = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
        fields: ['id', 'createdAt'],
        filters: {
            event: eventId,
        },
        populate: {
          event_attendee: {
            fields: ATTENDEE_FIELDS
          }
        },
        sort: 'createdAt:asc',
    });
    let attendees = eventAttendance.map(item => {
      return {
        registeredAt: item.createdAt,
        attendance_id: item.id,
        ...item.event_attendee[0],
      }
    });
    attendees = dropDuplicates(attendees, 'phone');

    return {
      ok: true,
      message: 'successful',
      attendees
    }
  },
  getSpcRegistration: async (id) => {
    const registration = await strapi.documents(`api::event-attendee.event-attendee`).findOne({
      documentId: id,
      fields: ATTENDEE_FIELDS
    });
    const eventAttendance = await strapi.documents(`api::event-attendance.event-attendance`).findMany({
        fields: ['id', 'createdAt'],
        filters: {
            event_attendee: id,
        },
        sort: 'createdAt:desc',
    });
    registration.registeredAt = eventAttendance[0].createdAt;
    return {
      ok: true,
      message: 'successful',
      registration
    }
  },
  getSomRegistrations: async (session) => {
    const registrations = await strapi.documents(`api::som-registration.som-registration`).findMany({
      fields: SOM_FIELDS,
      filters: {
        session: session,
      }
    });

    // convert snake case attribute names to camel case
    registrations.forEach(item => {
      item.otherNames = item.other_names;
      delete item.other_names;
      item.phoneNumber = item.phone_number;
      delete item.phone_number;
      item.dob = item.dob;
      item.address = item.address;
      item.occupation = item.occupation;
      item.photoUrl = item.photo_url;
      delete item.photo_url;
      item.maritalStatus = item.marital_status;
      delete item.marital_status;
      item.salvationStory = item.salvation_story;
      delete item.salvation_story;
      item.call = item.call;
      item.ministryExperience = item.ministry_experience;
      delete item.ministry_experience;
      item.whoIsPastorAyoToYou = item.who_is_pastor_ayo_to_you;
      delete item.who_is_pastor_ayo_to_you;
      item.isPetraMember = item.is_petra_member;
      delete item.is_petra_member;
      item.joinedPetraAt = item.joined_petra_at;
      delete item.joined_petra_at;
      item.joinedPetra = item.joined_petra;
      delete item.joined_petra;
      item.commitmentToPetra = item.commitment_to_petra;
      delete item.commitment_to_petra;
      item.howDidYouFindOut = item.how_did_you_find_out;
      delete item.how_did_you_find_out;
      item.ministry = item.ministry;
      item.positionInMinistry = item.position_in_ministry;
      delete item.position_in_ministry;
      item.session = item.session;
      delete item.session;
      item.surname = item.surname;
      item.email = item.email;
      item.code = item.code;
      item.weddingAnniversary = item.wedding_anniversary;
      delete item.wedding_anniversary;
      item.noOfChildren = item.no_of_children;
      delete item.no_of_children;
    });
    const ids = registrations.map(item => item.id);
    const payments = await strapi.documents(`api::som-payment.som-payment`).findMany({
      filters: {
        payer_id: {
          $in: ids,
        },
        payer_type: "registration",
      },
    });
    const messages = await strapi.documents(`api::som-message.som-message`).findMany({
      filters: {
        recipient_id: {
          $in: ids,
        },
      },
    });

    const reg_with_payments = registrations.map((registration) => {
      const payment = payments.find((payment) => payment.payer_id === registration.id);
      return {
        ...registration,
        payment,
      };
    });
    const reg_with_payment_and_messages = reg_with_payments.map((registration) => {
      registration.messages = messages.filter((message) => message.recipient_id === registration.id);
      return registration;
    });
    return {
      ok: true,
      message: 'successful',
      registrations: reg_with_payment_and_messages
    }
  },
  getSomRegistration: async (id) => {
    const registration = await strapi.documents(`api::som-registration.som-registration`).findOne({
      documentId: id,
      fields: SOM_FIELDS
    });
    if(! registration ) {
      return {
        ok: false,
        message: 'Registration not found',
      }
    }
    // convert snake case attribute names to camel case
    registration.otherNames = registration.other_names;
    delete registration.other_names;
    registration.phoneNumber = registration.phone_number;
    delete registration.phone_number;
    registration.dob = registration.dob;
    registration.address = registration.address;
    registration.occupation = registration.occupation;
    registration.photoUrl = registration.photo_url;
    delete registration.photo_url;
    registration.maritalStatus = registration.marital_status;
    delete registration.marital_status;
    registration.salvationStory = registration.salvation_story;
    delete registration.salvation_story;
    registration.call = registration.call;
    registration.ministryExperience = registration.ministry_experience;
    delete registration.ministry_experience;
    registration.whoIsPastorAyoToYou = registration.who_is_pastor_ayo_to_you;
    delete registration.who_is_pastor_ayo_to_you;
    registration.isPetraMember = registration.is_petra_member;
    delete registration.is_petra_member;
    registration.joinedPetraAt = registration.joined_petra_at;
    delete registration.joined_petra_at;
    registration.joinedPetra = registration.joined_petra;
    delete registration.joined_petra;
    registration.commitmentToPetra = registration.commitment_to_petra;
    delete registration.commitment_to_petra;
    registration.howDidYouFindOut = registration.how_did_you_find_out;
    delete registration.how_did_you_find_out;
    registration.ministry = registration.ministry;
    registration.positionInMinistry = registration.position_in_ministry;
    delete registration.position_in_ministry;
    registration.noOfChildren = registration.no_of_children;
    delete registration.no_of_children;
    registration.weddingAnniversary = registration.wedding_anniversary;
    delete registration.wedding_anniversary;

    let messages = await strapi.documents(`api::som-message.som-message`).findMany({
      filters: {
        recipient_id: registration.id,
      },
    });

    let payments = await strapi.documents(`api::som-payment.som-payment`).findMany({
      filters: {
        payer_id: registration.id,
        payer_type: "registration",
      },
    });

    let transactions = await strapi.documents(`api::som-transaction.som-transaction`).findMany({
      filters: {
        payer_id: registration.id,
      },
    });
    transactions = transactions.map((t) => ({
      amount: t.amount,
      status: t.status,
      id: Number(t.id),
      createdAt: t.createdAt,
      description: t.description,
    }))

    for (let i = 0; i < payments.length; i++) {
      const payment = payments[i];
      const payment_rebates = await strapi.documents(`api::rebate.rebate`).findMany({
        filters: {
          payment_id: payment.id,
        },
      });
      payment.rebates = payment_rebates;
    }
    let rebates = payments.flatMap((p) =>
      p.rebates.map((r) => ({
        amount: r.amount,
        id: Number(r.id),
        reason: r.reason,
      }))
    );

    payments = payments.map((payment) => {
      const { rebates, due_at: dueAt, amount, amount_paid: amountPaid } = payment;
      const totalRebates = rebates.reduce((acc, curr) => acc + curr.amount, 0);
      const outstandingAmount = amount - totalRebates - amountPaid;
      delete payment.rebates

      return {
        ...payment,
        id: Number(payment.id),
        amount: outstandingAmount,
        amountPaid: amountPaid,
        dueAt: dueAt,
      };
    });

    registration.paymentData = {
      payments,
      transactions,
      rebates,
    };
    registration.messages = messages;

    return {
      ok: true,
      message: 'successful',
      registration
    }
  },
  getPartners: async (page, limit) => {
    page = page || 1;
    // if no limit is set, count all records and set as default
    // count the no of records in the database
    const count = await strapi.documents(`api::partner.partner`).count();
    limit = limit || count;
    let start = (page - 1) * limit;
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        fields: PARTNER_FIELDS,
        sort: 'createdAt:desc',
        limit: limit,
        start: start,
    });

    return {
      ok: true,
      message: 'successful',
      partners
    }
  },
  getPartnerDetails: async (id) => {
    const partner = await strapi.documents(`api::partner.partner`).findOne({
      documentId: id,
      fields: PARTNER_FIELDS
    });
    if(! partner ) {
      return {
        ok: false,
        message: 'Partner not found',
      }
    }

    const payments = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
      filters: {
        partner_code: partner.partner_code,
      },
    });
    const unpaid = payments.filter((payment) => payment.status !== 'successful');
    const paid = payments.filter((payment) => payment.status === 'successful');

    partner.payments = {
      paid,
      unpaid
    }

    return {
      ok: true,
      message: 'successful',
      partner
    }
  },
  confirmPartnerOfflinePayment: async (partner_code, payment_id) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            partner_code: partner_code,
        },
    });
    const partner = partners[0];
    if(! partner ) {
      return {
        ok: false,
        message: 'Partner not found',
      }
    }
    await strapi.documents(`api::partner-payment.partner-payment`).update({
      documentId: payment_id,

      data: {
          offline_payment_status: 'successful',
          status: 'successful',
      }
    });
    return {
        message: 'Offline Payment confirmed, payment is now complete',
        ok: true,
    };
  },
  getPartnerPayments: async (start_date, end_date) => {
    start_date = start_date || '2024-01-01';
    end_date = end_date || new Date().toISOString().slice(0, 10);
    // if no limit is set, count all records and set as default
    // count the no of records in the database
    const count = await strapi.documents(`api::partner-payment.partner-payment`).count();
    const payments = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        sort: 'createdAt:desc',
        filters: {
          createdAt: {
            $between: [start_date, end_date],
          },
        },
        fields: ['id', 'partner_code', 'payment_date', 'currency', 'status', 'period', 'period_start', 'period_end', 'amount', 'offline_payment_status', 'createdAt']
    });

    return {
      ok: true,
      message: 'successful',
      payments
    }
  },
  getInternationalDelegate: async (id) => {
    const internationalDelegate = await strapi.documents('api::international-delegate.international-delegate').findOne({
      documentId: id
    });
    return {
      ok: true,
      message: 'successful',
      internationalDelegate
    }
  },
  getAllInternationalDelegate: async () => {
    try {
      const internationalDelegates = await strapi.documents('api::international-delegate.international-delegate').findMany({
      });

      if (!internationalDelegates || internationalDelegates.length === 0) {
        return {
          ok: true,
          data: [],
          message: 'No international delegates found',
        };
      }

      return {
        ok: true,
        data: internationalDelegates,
      };
    } catch (error) {
      console.error('Error international delegates:', error);
      return {
        ok: false,
        error: 'Failed to fetch international delegates',
      };
    }
  },
  getSOMRegistrations: async () => {
    const { session } = await strapi.service('api::som-api.som-api').getCurrentSession();
    const registrations = await strapi.documents(`api::som-registration.som-registration`).findMany({
      fields: ['id', 'email'],
      filters: {
        session: session,
      }
    });
    const ids = registrations.map(item => item.id);
    const payments = await strapi.documents(`api::som-payment.som-payment`).findMany({
      filters: {
        payer_id: {
          $in: ids,
        },
        payer_type: "registration",
      },
    });
    // join registrations and payments based on id and payer_id
    return registrations.map((registration) => {
      const payment = payments.find((payment) => payment.payer_id === registration.id);
      return {
        ...registration,
        payment,
      };
    });
  },
  sendSomBulkAcceptanceMail: async () => {
    const { session } = await strapi.service('api::som-api.som-api').getCurrentSession();
    const registrations = await strapi.service('api::office-api.office-api').getSOMRegistrations();
    const paid_registrations = registrations.filter(item => item.payment !== undefined && item.payment.status === 'success');
    let results = {};
    for(let item of paid_registrations) {
      try {
        let res = await strapi.service('api::office-api.emails').sendAcceptanceEmail(item.id);
        results[item.email] = res;
      } catch (err) {
        console.log(err);
        results[item.email] = err;
      }
    }
    return {
      ok: true,
      message: 'Bulk task completed',
      results: results,
    };
  },
  sendSomInfoMail: async (query) => {
    let params = {
      template_text: query.template_text || null,
      template_to_send: query.template_to_send || 'som_update_mail',
      group: query.group || 'all',
      title: query.title || `Welcome to School of Ministry – Important Information Inside!`,
    }
    let registrations = await strapi.service('api::office-api.office-api').getSOMRegistrations();

    switch(params.group) {
      case 'paid':
        registrations = registrations.filter(item => item.payment !== undefined && item.payment.status === 'success');
        break;
      case 'unpaid':
        registrations = registrations.filter(item => item.payment === undefined || item.payment.status !== 'success');
        break;
    }

    let template = GENERIC_EMAIL_TEMPLATES[params.template_to_send];
    let template_params = {};

    // If template_text is provided and not empty, use it and set template to null
    if (params.template_text && params.template_text.trim() !== '') {
      template = null;
    } else {
      switch (params.template_to_send) {
        case 'som_info_mail':
          const { session, group_chat_link, start_date, venue } = await strapi.service('api::som-api.som-api').getCurrentSession();
          template_params = { group_chat_link, start_date, venue };
          break;
      }
    }

    const emails = registrations.map(item => item.email);

    return await strapi.service('api::office-api.emails').sendEmail(
      emails,
      params.title,
      template,
      template_params,
      params.template_text
    );
  },
});
