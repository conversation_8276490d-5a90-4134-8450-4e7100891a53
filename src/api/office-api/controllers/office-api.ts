// @ts-nocheck
/**
 * A set of functions called "actions" for `office-api`
 */

export default {
  getSpcRegistrations: async (ctx, next) => {
    const latestSPC = 'Sons and Protégé Conference 2024';
    try {
      let res = await strapi.service('api::office-api.office-api').getSpcRegistrations(latestSPC);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        attendees: res.attendees,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getSpcRegistration: async (ctx, next) => {
    try {
      const id = ctx.params.id;
      let res = await strapi.service('api::office-api.office-api').getSpcRegistration(id);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        registration: res.registration,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getSomRegistrations: async (ctx, next) => {
    let currentSession;
    if(ctx.query.session) {
      currentSession = ctx.query.session;
    } else {
      const { session } = await strapi.service('api::som-api.som-api').getCurrentSession();
      currentSession = session;
    }
    try {
      let res = await strapi.service('api::office-api.office-api').getSomRegistrations(currentSession);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        registrations: res.registrations,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getSomRegistration: async (ctx, next) => {
    try {
      const id = ctx.params.id;
      let res = await strapi.service('api::office-api.office-api').getSomRegistration(id);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        registration: res.registration,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  sendAcceptanceEmail: async (ctx, next) => {
    try {
      const id = ctx.params.id;
      let res = await strapi.service('api::office-api.emails').sendAcceptanceEmail(id);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getPartners: async (ctx, next) => {
    try {
      const { page, limit } = ctx.query;
      let res = await strapi.service('api::office-api.office-api').getPartners(page, limit);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        partners: res.partners,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getPartnerDetails: async (ctx, next) => {
    try {
      const id = ctx.params.id;
      let res = await strapi.service('api::office-api.office-api').getPartnerDetails(id);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        partner: res.partner,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  confirmPartnerOfflinePayment: async (ctx, next) => {
    try {
      const { partner_code, payment_id } = ctx.request.body;
      if (! payment_id || ! partner_code) {
        ctx.body = {
          message: 'Payment ref is required',
          ok: false,
        };
        ctx.status = 400;
        return;
      }
      let res = await strapi.service('api::office-api.office-api').confirmPartnerOfflinePayment(partner_code, payment_id);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getPartnerPayments: async (ctx, next) => {
    try {
      const { start_date, end_date } = ctx.query;
      let res = await strapi.service('api::office-api.office-api').getPartnerPayments(start_date, end_date);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
        payments: res.payments,
      };
    } catch (err) {
      ctx.body = err;
    }
  },
  getInternationalDelegate: async (ctx) => {
    try {
      const { id } = ctx.params;
      const announcement = await strapi.service('api::office-api.office-api').getInternationalDelegate(id);
      if (!announcement) {
        ctx.status = 404;
        ctx.body = { error: 'International delegate not found' };
        return;
      }
      ctx.body = announcement;
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  getAllInternationalDelegate: async (ctx) => {
    try {
      const res = await strapi.service('api::generic-api.generic-api').getAllInternationalDelegate();
      if (res.ok) {
        ctx.body = res.data;
      } else {
        ctx.body = res.error;
        ctx.status = 500;
      }
    } catch (err) {
      ctx.body = err;
      ctx.status = 500;
    }
  },
  sendSomInfoMail: async (ctx, next) => {
    try {
      let res = await strapi.service('api::office-api.office-api').sendSomInfoMail(ctx.query);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = ctx.body = res;
    } catch (err) {
      ctx.body = err;
    }
  },
  sendSomBulkAcceptanceMail: async (ctx, next) => {
    let res = await strapi.service('api::office-api.office-api').sendSomBulkAcceptanceMail();
    if (! res || ! res.ok) {
      ctx.body = res;
      ctx.status = 400;
      return;
    }
    ctx.body = res;
  }
};
