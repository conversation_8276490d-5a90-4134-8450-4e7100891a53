// @ts-nocheck
export default {
  routes: [
    {
     method: 'GET',
     path: '/office-api/spc-registrations',
     handler: 'office-api.getSpcRegistrations',
     config: {
       policies: [],
       middlewares: ["api::office-api.is-admin"],
     },
    },
    {
      method: 'GET',
      path: '/office-api/spc-registrations/:id',
      handler: 'office-api.getSpcRegistration',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/som-registrations',
      handler: 'office-api.getSomRegistrations',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/som-registrations/:id',
      handler: 'office-api.getSomRegistration',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/som-registrations/:id/send-acceptance-email',
      handler: 'office-api.sendAcceptanceEmail',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/partners',
      handler: 'office-api.getPartners',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/partners/all-payments',
      handler: 'office-api.getPartnerPayments',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
       method: 'GET',
       path: '/office-api/partners/:id',
       handler: 'office-api.getPartnerDetails',
       config: {
         policies: [],
         middlewares: ["api::office-api.is-admin"],
       },
    },
    {
      method: 'PUT',
      path: '/office-api/partners/confirm-offline-payment',
      handler: 'office-api.confirmPartnerOfflinePayment',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/foreign-delegates/get/:id',
      handler: 'office-api.getInternationalDelegate',
      config: {
        policies: [],
        middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/send-som-info-mail',
      handler: 'office-api.sendSomInfoMail',
      config: {
        auth: false,
        policies: [],
        //middlewares: ["api::office-api.is-admin"],
      },
    },
    {
      method: 'GET',
      path: '/office-api/send-som-bulk-acceptance-mail',
      handler: 'office-api.sendSomBulkAcceptanceMail',
      config: {
        auth: false,
        policies: [],
        //middlewares: ["api::office-api.is-admin"],
      },
    },
  ],
};
