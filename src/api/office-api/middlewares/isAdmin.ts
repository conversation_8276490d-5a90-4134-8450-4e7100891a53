// @ts-nocheck
/**
 * `isAdmin` middleware
 */

import { Strapi } from '@strapi/strapi';

export default (config, { strapi }: { strapi: Strapi }) => {
  return async (ctx, next) => {
    // strapi.log.info('In isAdmin middleware.');
    const user = ctx.state.user;
    if ( user.role.name !== 'Office Admin') {
      return ctx.unauthorized('Unauthorized', 'You need admin privileges to access this resource.')
    }

    await next();
  };
};
