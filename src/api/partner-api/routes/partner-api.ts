// @ts-nocheck
export default {
  routes: [
    {
     method: 'POST',
     path: '/partner-api/register',
     handler: 'partner-api.register',
     config: {
      auth: false,
      policies: [],
      middlewares: [],
     },
    },
    {
      method: 'POST',
      path: '/partner-api/login',
      handler: 'partner-api.login',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/partner-api/me',
      handler: 'partner-api.getPartner',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/partner-api/my/payments',
      handler: 'partner-api.getMyPayments',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/partner-api/get-payment-link',
      handler: 'partner-api.getPaymentLink',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/partner-api/flw-redirect',
      handler: 'partner-api.flwRedirect',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/partner-api/flw-redirect',
      handler: 'partner-api.flwRedirect',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/partner-api/register-offline-payment',
      handler: 'partner-api.registerOfflinePayment',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/partner-api/run-transaction-generation',
      handler: 'partner-api.testCron',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/partner-api/one-time-payment',
      handler: 'partner-api.oneTimePayment',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/partner-api/send-password-reminder',
      handler: 'partner-api.sendPasswordReminder',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/partner-api/update-partner',
      handler: 'partner-api.updatePartner',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
  ],
};
