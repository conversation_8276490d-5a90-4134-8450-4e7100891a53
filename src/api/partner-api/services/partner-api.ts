// @ts-nocheck
import axios from 'axios';
const Flutterwave = require('flutterwave-node-v3');
const flw = new Flutterwave(process.env.FLW_PUBLIC_KEY, process.env.FLW_SECRET_KEY);
const FIELDS = ['firstname', 'surname', 'email', 'phone', 'city', 'country', 'amount', 'currency', 'frequency', 'partner_code'];
const PAYMENT_FIELDS = ['partner_code', 'amount', 'currency', 'payment_date', 'flw_payload', 'status', 'period', 'period_start', 'period_end', 'txn_ref', 'payment_link', 'description', 'createdAt', 'updatedAt'];

/**
 * Function to generate partner code starting with 'PETRA-' and 5 random characters
 * @returns {string} partner code
 * @example PETRA-3j4k5l6
 */
const makePartnerCode = () => {
  const code = (""+Math.random()).substring(2,7);
  return `PETRA-${code}`;
}
/**
 * Function to generate a partner code and check if it exists, if yes generate another one
 *
 * @returns {string} partner code
 */
const generatePartnerCode = async () => {
  let partnerCode = makePartnerCode();
  let partnerWithCode = await strapi.documents(`api::partner.partner`).findMany({
    filters: {
      partner_code: partnerCode,
    },
  });
  while (partnerWithCode.length > 0) {
    partnerCode = makePartnerCode();
    partnerWithCode = await strapi.entityService.find(`api::partner.partner`, {
      filters: {
        partner_code: partnerCode,
      },
    });
  }
  return partnerCode;
}

/**
 * Function to get the start and end dates of a period
 * @param period
 * @returns {{endDate: Date, startDate: Date}}
 * @example
 *  const currentPeriod = "monthly";
 *  const dates = getPeriodStartEndDates(currentPeriod);
 *  console.log("Start Date:", dates.startDate);
 *  console.log("End Date:", dates.endDate);
 */
const getPeriodStartEndDates = (period) => {
  const today = new Date();
  const startDate = new Date(today);
  const endDate = new Date(today);

  switch (period.toLowerCase()) {
    case "daily":
      endDate.setDate(endDate.getDate() + 1);
      break;
    case "weekly":
      startDate.setDate(startDate.getDate() - startDate.getDay()); // Start on Monday
      endDate.setDate(endDate.getDate() + (6 - endDate.getDay())); // End on Sunday
      break;
    case "monthly":
      startDate.setDate(1);
      endDate.setMonth(endDate.getMonth() + 1, 0); // Last day of the month
      break;
    case "quarterly":
      const quarter = Math.floor((endDate.getMonth() + 2) / 3);
      startDate.setMonth(3 * quarter - 2);
      endDate.setMonth(3 * quarter + 1, 0); // Last day of the quarter
      break;
    case "yearly":
      startDate.setMonth(0, 1); // January 1st
      endDate.setMonth(11, 31); // December 31st
      break;
    default:
      throw new Error(`Invalid period: ${period}`);
  }

  return { startDate, endDate };
 };

 /**
  * Function to generate a payment link for a partner
  * @param partner
  * @param payment
 */
 const generateFlwPaymentLink = async (partner, payment) => {
  if (payment.status === 'successful') {
      return {
          message: 'Payment already made',
          ok: false,
      };
  }

  const flw_payload = {
    tx_ref: payment.txn_ref,
    amount: payment.amount,
    currency: payment.currency,
    redirect_url: process.env.FLW_REDIRECT,
    customer: {
      email: partner.email,
      phone_number: partner.phone,
      name: `${partner.firstname.toUpperCase()} ${partner.surname.toUpperCase()}`,
    },
    customizations: {
      title: 'Tribe Petra World Ministries',
      description: payment.description,
      logo: 'https://res.cloudinary.com/dazocl67q/image/upload/v1704472625/Website%20Assets/AAMI/Logo_AA_2x_1_ufhpmy.png',
    },
  }

  try {
      const response = await axios.post(process.env.FLW_PAYMENTS_URL, flw_payload, {
          headers: {
              Authorization: `Bearer ${process.env.FLW_SECRET_KEY}`,
              'Content-Type': 'application/json'
          },
      });

      const data = response.data;
      // save the payment link to the payment
      await strapi.documents(`api::partner-payment.partner-payment`).update({
          documentId: payment.documentId,

          data: {
              payment_link: data.data.link,
              flw_payload: JSON.stringify(data),
          }
      });
      // return the payment link
      return {
          ok: true,
          message: 'Success',
          payment_link: data.data.link,
      };
  } catch (err) {
      console.log(err.response.data);
      console.log(err.response.data.errors);
      return {
          message: err.response.data.message,
          ok: false,
      };
  }
 }

/**
 * partner-api service
 */
export default () => ({
  register: async (data) => {
    const partner = await strapi.documents(`api::partner.partner`).findMany({
        fields: FIELDS,
        filters: {
            email: data.email,
        },
    });
    if (partner.length > 0) {
      return {
        message: 'Partner with email already exists',
        ok: false,
      };
    }

    const newPartner = await strapi.documents(`api::partner.partner`).create({
        data: {
            ...data,
            partner_code: await generatePartnerCode(),
        }
    });
    return {
        message: 'Partner created successfully',
        ok: true,
        partner: newPartner,
    };
  },
  login: async (data) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            partner_code: data.partner_code,
        },
    });
    // if no user with the partner code, return error
    if (partners.length === 0) {
        return {
            message: 'Partner does not exist',
            ok: false,
        };
    }
    const partner = partners[0];
    // if the user exists, check if the password is the phone number
    if (partner.phone !== data.password) {
        return {
            message: 'Invalid phone number',
            ok: false,
        };
    }

    const user = await strapi.documents(`plugin::users-permissions.user`).findMany({
        fields: ['id', 'email'],
        filters: {
          email: partner.email,
        },
    });
    const jwt = await strapi.plugins['users-permissions'].services.jwt.issue({
        id: user[0].id,
    });
    return {
        ok: true,
        partner,
        jwt,
    };
  },
  getPartner: async (email) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            email: email,
        },
    });
    const partner = partners[0];
    return {
        ok: true,
        partner,
    };
  },
  getMyPayments: async (email) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            email: email,
        },
    });
    const partner = partners[0];
    const { startDate, endDate } = getPeriodStartEndDates(partners[0].frequency);
    let { start, end } = {
        start: startDate.toISOString().slice(0, 10),
        end: endDate.toISOString().slice(0, 10),
    }
    const current_payment = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        fields: ['id'],
        filters: {
            partner_code: partner.partner_code,
            period_start: start,
            period_end: end,
            period: partner.frequency,
        },
        sort: 'createdAt:desc',
    });
    // if empty, generate new payment
    if (current_payment.length === 0) {
        const data = {
            partner_code: partner.partner_code,
            amount: partner.amount,
            currency: partner.currency,
            period_start: start,
            period_end: end,
            period: partner.frequency,
            txn_ref: `${partner.partner_code}/${start}/${end}`,
            description: `${partner.frequency.charAt(0).toUpperCase()+partner.frequency.slice(1)} payment for the period ${start} to ${end}`,
        };
        await strapi.documents(`api::partner-payment.partner-payment`).create({
            data
        });
      }
      // fetch two arrays of paid and unpaid payments
      const payments = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        filters: {
            partner_code: partner.partner_code,
            status: 'successful',
            // period: {
            //   $ne: 'one-time',
            // },
        },
    });
    const outstanding = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        filters: {
            partner_code: partner.partner_code,
            period: {
                $ne: 'one-time',
              },
            status: {
                $ne: 'successful', // 'pending' or 'failed'
            },
          },
    });
    const one_time = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        filters: {
            partner_code: partner.partner_code,
            period: 'one-time',
            status: {
                $ne: 'successful', // 'pending' or 'failed'
            },
          },
    });
    return {
        message: 'Success',
        ok: true,
        payments,
        outstanding,
        one_time
    };
  },
  // Use the following for troublshooting: https://developer.flutterwave.com/docs/collecting-payments/standard
  // https://developer.flutterwave.com/docs/integration-guides/testing-helpers/#cards
  getPaymentLink: async (email, txn_ref) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            email: email,
        },
    });
    const partner = partners[0];

    const txns = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        filters: {
            partner_code: partner.partner_code,
            txn_ref: txn_ref,
        }
    });

    if (txns.length === 0) {
      return {
            message: 'Invalid transaction reference',
            ok: false,
      };
    }
    const current_payment = txns[0];
    return await generateFlwPaymentLink(partner, current_payment);
  },
  flwRedirect: async (query) => {
    if (query.status === 'successful') {
        const txns = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
            filters: {
                txn_ref: query.tx_ref,
            },
        });
        if (txns.length === 0) {
            return {
                message: 'Invalid transaction reference',
                ok: false,
            };
        }
        const transactionDetails = txns[0];
        const response = await flw.Transaction.verify({id: query.transaction_id});
        let status = transactionDetails.status;
        let flw_response = {};
        if (
            response.data
            && response.data.status === "successful"
            && response.data.amount === transactionDetails.amount
            && response.data.currency === transactionDetails.currency) {
              status = 'successful';
              flw_response = response.data;
        } else {
            status = 'failed';
            flw_response = response.data || response;
        }
        await strapi.documents(`api::partner-payment.partner-payment`).update({
            documentId: transactionDetails.documentId,

            data: {
                status: status,
                flw_payload: JSON.stringify(flw_response),
                payment_date: new Date(),
            }
        });
        return {
            message: 'Redirecting to frontend',
            ok: true,
            redirect_url: `${process.env.FRONTEND_URL}/partnership/${transactionDetails.partner_code}&status=${status}`,
        };
    } else {
        return {
            message: 'Redirecting to frontend',
            ok: false,
            redirect_url: `${process.env.FRONTEND_URL}/partnership/${transactionDetails.partner_code}&status=${status}`,
        };
    }
  },
  sendWelcomeEmail: async (email, partnerCode) => {
    const email_content = `<div><p>Thank you for registering as a partner with Ayo Ajani Ministries. </p><p>Your partner code is <b>${partnerCode}</b> and you can login using your phone number as your password. </p><p>Please login to make payment by visiting ${process.env.AACOM_URL}/partnership. </p><p>Thank you once again for joining hands with me in partnership.</div>`;

    await strapi.plugins['email'].services.email.send({
      to: email,
      from: '',
      subject: 'Welcome Partner',
      text: email_content,
      html: `<p>${email_content}</p>`,
    });
  },
  createAndLoginUser: async (partner) => {
    let user = {};
    const users = await strapi.plugins['users-permissions'].services.user.fetchAll();
    if (users.filter((u) => u.email === partner.email).length === 0) {
      user = await strapi.plugins['users-permissions'].services.user.add({
        username: partner.partner_code,
        email: partner.email,
        password: partner.phone,
        role: 1,
        provider: 'local',
      });
    } else{
      user = users[0];
    }

    // log the user in
    const jwt = await strapi.plugins['users-permissions'].services.jwt.issue({
      id: user.id,
    });
    return jwt;
  },
  registerOfflinePayment: async (partnerCode, payment_id) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            partner_code: partnerCode,
        },
    });
    const partner = partners[0];
    if(! partner ) {
      return {
        ok: false,
        message: 'Partner not found',
      }
    }
    await strapi.documents(`api::partner-payment.partner-payment`).update({
        documentId: payment_id,

        data: {
            offline_payment_status: 'awaiting confirmation',
        }
    });
    return {
        message: 'Offline Payment registered, awaiting cofirmation from admin',
        ok: true,
    };
  },
  generatePeriodicTransactions: async () => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        fields: FIELDS,
    });

    partners.forEach(async (partner) => {
        const { startDate, endDate } = getPeriodStartEndDates(partner.frequency);
        let { start, end } = {
            start: startDate.toISOString().slice(0, 10),
            end: endDate.toISOString().slice(0, 10),
        }
        // log start and end dates and partner code
        strapi.log.info('Start Date:', start);
        strapi.log.info('End Date:', end);
        strapi.log.info('Partner Code:', partner.partner_code);
        const current_payment = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
            fields: ['id'],
            filters: {
                partner_code: partner.partner_code,
                period_start: start,
                period_end: end,
                period: partner.frequency,
            },
        });
        // if empty, generate new payment
        if (current_payment.length === 0) {
          strapi.log.info(`No transaction found for period: ${start} to ${end} . Generating partner periodic transaction for partner: ${partner.partner_code}`);
            const data = {
                partner_code: partner.partner_code,
                amount: partner.amount,
                currency: partner.currency,
                period_start: start,
                period_end: end,
                period: partner.frequency,
                txn_ref: `${partner.partner_code}/${start}/${end}`,
                description: `${partner.frequency.charAt(0).toUpperCase()+partner.frequency.slice(1)} payment for the period ${start} to ${end}`,
            };
            console.log('creating new transaction')
            await strapi.documents(`api::partner-payment.partner-payment`).create({
                data
            });
            // send an email to the partner
            console.log('sending email now');
            await strapi.services['api::office-api.emails'].sendTransactionGeneratedEmail(partner, start, end);
            console.log('Email sent successfully...');
        } else {
          console.log('transaction already exists');
        }
    });
    return {
        message: 'Periodic transactions generated successfully',
        ok: true,

    };
  },
  oneTimePayment: async (email, amount, currency) => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        filters: {
            email: email,
        },
    });
    const partner = partners[0];
    const { startDate, endDate } = getPeriodStartEndDates(partner.frequency);
    let { start, end } = {
        start: startDate.toISOString().slice(0, 10),
        end: endDate.toISOString().slice(0, 10),
    }
    const now = Date.now();
    const data = {
      partner_code: partner.partner_code,
      amount: amount,
      currency: currency,
      period_start: start,
      period_end: end,
      period: 'one-time',
      txn_ref: `${partner.partner_code}/${start}/${end}/${now}`,
      description: `One-Time payment for the period ${start} to ${end}`,
    };
    const payment = await strapi.documents(`api::partner-payment.partner-payment`).create({
        data
    });

    const res = await generateFlwPaymentLink(partner, payment);
    return {
      ...res,
      payment: {
        ...payment,
        payment_link: res.payment_link
      }
    }
  },
  sendPasswordReminder: async () => {
    const partners = await strapi.documents(`api::partner.partner`).findMany({
        fields: FIELDS,
    });

    partners.forEach(async (partner) => {
      // send an email to the partner
      console.log('sending email now');
      await strapi.services['api::office-api.emails'].sendPasswordReminderEmail(partner);
      console.log('Email sent successfully...');
    });
    return {
        message: 'Password reminders sent to partners',
        ok: true,

    };
  },
  markOfflineTransactionsComplete: async () => {
    const payments = await strapi.documents(`api::partner-payment.partner-payment`).findMany({
        fields: FIELDS,
        filters: {
            offline_payment_status: 'awaiting confirmation',
        },
    });
    payments.forEach(async (payment) => {
      await strapi.documents(`api::partner-payment.partner-payment`).update({
          documentId: payment.documentId,

          data: {
              offline_payment_status: 'successful',
              status: 'successful'
          }
      });
    });
    return {
        message: 'Offline pending transactions marked as complete',
        ok: true,

    };
  },
  updatePartner: async (data) => {
    const partner = await strapi.documents(`api::partner.partner`).findOne({
        documentId: data.documentId
    });
    if(! partner) {
      return {
        message: 'Partner not found',
        ok: false,
      }
    }

    const updatedPartner = await strapi.documents(`api::partner.partner`).update({
        documentId: data.documentId,
        data
    });
    return {
        message: 'Partner updated successfully',
        ok: true,
        partner: updatedPartner,
    };
  },
});
