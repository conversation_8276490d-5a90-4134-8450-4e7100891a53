// @ts-nocheck
/**
 * A set of functions called "actions" for `partner-api`
 */

export default {
  register: async (ctx, next) => {
    try {
      // validate the form
      const { email, firstname, surname, phone, amount } = ctx.request.body;
      if (! email || ! firstname || ! surname || ! phone || ! amount) {
        ctx.body = {
          message: 'All fields are required',
          ok: false,
        };
        ctx.status = 400;
        return;
      }
      let res = await strapi.service('api::partner-api.partner-api').register(ctx.request.body);

      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }

      let partner = res.partner;
      let jwt = await strapi.service('api::partner-api.partner-api').createAndLoginUser(partner);
      await strapi.service('api::partner-api.partner-api').sendWelcomeEmail(partner.email, partner.partner_code);
      ctx.body = {
        partner,
        token: jwt,
        message: 'New Partner Registeration Completed'
      };
     } catch (err) {
       console.log(err);
       ctx.body = err;
     }
  },
  login: async (ctx, next) => {
    try {
      let res = await strapi.service('api::partner-api.partner-api').login(ctx.request.body);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: 'Partner Login Successful',
        partner: res.partner,
        jwt: res.jwt,
      };
     } catch (err) {
       ctx.body = err;
     }
  },
  getPartner: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }
    const { email } = ctx.state.user;
    try {
      let res = await strapi.service('api::partner-api.partner-api').getPartner(email);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  getMyPayments: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }
    const { email } = ctx.state.user;
    try {
      let res = await strapi.service('api::partner-api.partner-api').getMyPayments(email);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  getPaymentLink: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }

    const { txn_ref } = ctx.request.body;
    if (! txn_ref) {
      ctx.body = {
        message: 'Transaction Reference is required',
        ok: false,
      };
      ctx.status = 400;
      return;
    }

    const { email } = ctx.state.user;
    try {
      let res = await strapi.service('api::partner-api.partner-api').getPaymentLink(email, txn_ref);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: 'Payment link generated successfully',
        payment_link: res.payment_link,
      };
     } catch (err) {
       ctx.body = err;
     }
  },
  flwRedirect: async (ctx, next) => {
    try {
      let res = await strapi.service('api::partner-api.partner-api').flwRedirect(ctx.request.query);
      ctx.redirect(res.redirect_url);
    } catch (err) {
      ctx.body = err;
    }
  },
  registerOfflinePayment: async (ctx, next) => {
    const { partner_code, payment_id } = ctx.request.body;
    if (! payment_id || ! partner_code) {
      ctx.body = {
        message: 'Payment ref is required',
        ok: false,
      };
      ctx.status = 400;
      return;
    }
    // use the service to confirm offline payment
    try {
      let res = await strapi.service('api::partner-api.partner-api').registerOfflinePayment(partner_code, payment_id);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {
        message: res.message,
      };
     } catch (err) {
       ctx.body = err;
     }
  },
  testCron: async (ctx, next) => {
    let res = await strapi.service('api::partner-api.partner-api').generatePeriodicTransactions();
    return res;
  },
  oneTimePayment: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }
    const { email } = ctx.state.user;
    const { amount, currency } = ctx.request.body;
    if (! amount || ! currency) {
      ctx.body = {
        message: 'Amount and Currency are required',
        ok: false,
      };
      ctx.status = 400;
      return;
    }
    try {
      let res = await strapi.service('api::partner-api.partner-api').oneTimePayment(email, amount, currency);
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  sendPasswordReminder: async (ctx, next) => {
    try {
      let res = await strapi.service('api::partner-api.partner-api').sendPasswordReminder();
      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  updatePartner: async (ctx, next) => {
    try {
      // validate the form
      const { id, email, firstname, surname, phone, amount, currency, frequency } = ctx.request.body;
      if (! id || ! email || ! firstname || ! surname || ! phone || ! amount || ! currency || ! frequency) {
        ctx.body = {
          message: 'All fields are required',
          ok: false,
        };
        ctx.status = 400;
        return;
      }
      let res = await strapi.service('api::partner-api.partner-api').updatePartner(ctx.request.body);

      if (! res || ! res.ok) {
        ctx.body = res;
        ctx.status = 400;
        return;
      }

      let partner = res.partner;
      ctx.body = {
        partner,
        message: 'Partner Update Completed'
      };
     } catch (err) {
       console.log(err);
       ctx.body = err;
     }
  },
};
