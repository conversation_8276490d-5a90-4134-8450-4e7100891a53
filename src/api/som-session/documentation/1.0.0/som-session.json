{"/som-sessions": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SomSessionListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Som-session"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/som-sessions"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SomSessionResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Som-session"], "parameters": [], "operationId": "post/som-sessions", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SomSessionRequest"}}}}}}, "/som-sessions/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SomSessionResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Som-session"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/som-sessions/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SomSessionResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Som-session"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/som-sessions/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SomSessionRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Som-session"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/som-sessions/{id}"}}}