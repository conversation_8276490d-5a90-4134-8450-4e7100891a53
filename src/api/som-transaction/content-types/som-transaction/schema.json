{"kind": "collectionType", "collectionName": "som_transactions", "info": {"singularName": "som-transaction", "pluralName": "som-transactions", "displayName": "SOM Transaction", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"payment_id": {"type": "integer"}, "status": {"type": "string", "default": "pending"}, "channel": {"type": "string"}, "currency": {"type": "string", "default": "NGN"}, "amount": {"type": "decimal", "default": 0}, "payer_id": {"type": "integer"}, "description": {"type": "string"}, "meta": {"type": "json"}}}