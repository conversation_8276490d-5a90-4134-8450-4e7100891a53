{"kind": "collectionType", "collectionName": "event_sessions", "info": {"singularName": "event-session", "pluralName": "event-sessions", "displayName": "Event Session"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "date_time": {"pluginOptions": {"i18n": {"localized": true}}, "type": "datetime"}}}