/**
 * A set of functions called "actions" for `souls-api`
 */

export default {
  saveConvert: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }
    try {
      const { id: userId } = ctx.state.user;
      let res = await strapi.service('api::souls-api.souls-api').saveConvert(ctx.request.body, userId);
      ctx.body = {
        data: res,
        message: 'New Convert Recorded Successfully'
      };
     } catch (err) {
       ctx.body = err;
     }
  },
  getMyConvertsCount: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }
    try {
      const { id: userId } = ctx.state.user;
      let res = await strapi.service('api::souls-api.souls-api').getMyConverts(userId);
      ctx.body = {converts: res.length};
     } catch (err) {
       ctx.body = err;
     }
  },
  getMyConverts: async (ctx, next) => {
    if(! ctx.state.isAuthenticated) {
      return ctx.unauthorized('Unauthenticated', 'You need to login first to access this resource.')
    }
    const { id: userId } = ctx.state.user;
    try {
      let res = await strapi.service('api::souls-api.souls-api').getMyConverts(userId);
      ctx.body = {data: res};
     } catch (err) {
       ctx.body = err;
     }
  },
  getAllConverts: async (ctx, next) => {
    try {
      let res = await strapi.service('api::souls-api.souls-api').getAllConverts();
      ctx.body = res;
     } catch (err) {
       ctx.body = err;
     }
  },
};
