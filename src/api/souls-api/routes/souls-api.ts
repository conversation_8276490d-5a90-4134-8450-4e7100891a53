export default {
  routes: [
    {
     method: 'POST',
     path: '/souls-api/my/new-converts',
     handler: 'souls-api.saveConvert',
     config: {
       policies: [],
       middlewares: [],
     },
    },
    {
      method: 'GET',
      path: '/souls-api/my/new-converts/count',
      handler: 'souls-api.getMyConvertsCount',
      config: {
        policies: [],
        middlewares: [],
      },
     },
     {
      method: 'GET',
      path: '/souls-api/my/new-converts',
      handler: 'souls-api.getMyConverts',
      config: {
        policies: [],
        middlewares: [],
      },
     },
     {
      method: 'GET',
      path: '/souls-api/all/new-converts/count',
      handler: 'souls-api.getAllConverts',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
     },
  ],
};
