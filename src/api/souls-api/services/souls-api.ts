// @ts-nocheck
/**
 * souls-api service
 */

const FIELDS = ['firstname', 'lastname', 'email', 'phone', 'gender', 'address', 'marital_status', 'ministry_materials', 'prayer_request'];

export default () => ({
    saveConvert: async (data, userId) => {
        const info = await strapi.documents(`api::convert.convert`).create({
            data: {
                ...data,
                converted_by: userId
            }
        });
        return info;
    },
    getMyConverts: async (userId) => {
        const info = await strapi.documents(`api::convert.convert`).findMany({
            fields: FIELDS,
            filters: {
                converted_by: userId,
            },
        });
        return info;
    },
    getAllConverts: async () => {
        const info = await strapi.documents(`api::convert.convert`).findMany({
            fields: FIELDS,
        });
        return {
            message: 'Success',
            converts: info.length
        };
    }
});
