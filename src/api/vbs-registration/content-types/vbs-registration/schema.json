{"kind": "collectionType", "collectionName": "vbs_registrations", "info": {"singularName": "vbs-registration", "pluralName": "vbs-registrations", "displayName": "VBS Registration", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"parent_first_name": {"type": "string"}, "parent_last_name": {"type": "string"}, "parent_email": {"type": "email"}, "parent_phone": {"type": "string"}, "petra_member": {"type": "enumeration", "enum": ["Yes", "No"]}, "number_of_children": {"type": "integer", "default": 1}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::tribe-petra-kid.tribe-petra-kid"}, "year": {"type": "string", "maxLength": 4, "minLength": 4, "default": "2023"}}}