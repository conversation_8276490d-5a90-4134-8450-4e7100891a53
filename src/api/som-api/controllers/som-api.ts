// @ts-nocheck
/**
 * A set of functions called "actions" for `som-api`
 */
export default {
  register: async (ctx, next) => {
    try {
      // get session and check if it is open
      const { session, can_register, start_date, end_date, contact, location } = await strapi.service('api::som-api.som-api').getCurrentSession();
      if (!session || !can_register) {
        ctx.body = {
          error: `Registration is closed at the moment`,
        };
        ctx.status = 400;
        return;
      }
      // validate the form
      const data = await strapi.service('api::som-api.som-api').validateRegistrationData(ctx.request.body);
      if (!data || !data.ok) {
        ctx.body = data.error;
        ctx.status = 400;
        return;
      }

      const res = await strapi.service('api::som-api.som-api').register(data.data, session);
      if (!res || !res.ok) {
        ctx.body = res.error;
        ctx.status = 400;
        return;
      }

      // send registration email
      console.log('res is ', res);
      if (
        !(
            res.data &&
            res.data.message &&
            typeof res.data.message === "string" &&
            res.data.message.startsWith("You have already registered for")
        )
    ) {
        await strapi
            .service("api::som-api.som-api")
            .sendRegistrationEmail(
                res.data.data,
                session,
                start_date,
                end_date,
                contact,
                location
            );
    }

      ctx.body = res.data;
    } catch (err) {
      console.log(err);
      ctx.status = 400;
      ctx.body = {
        error: `There was a problem processing your registration, please try again`,
      };
    }
  },
  getOutstandingPayments: async (ctx, next) => {
    const { email, code } = ctx.request.query;

    if (!email || !code) {
      ctx.body = {
        error: `Email and code are required`,
      };
      ctx.status = 400;
      return;
    }
    const res = await strapi.service('api::som-api.som-api').getOutstandingPayments(email, code);
    if (!res || !res.ok) {
      ctx.body = res.error;
      ctx.status = 400;
      return;
    }
    ctx.body = res.data;
  },
  handleVerifyPayment: async (ctx, next) => {
    try {
      let res = await strapi.service('api::som-api.som-api').handleVerifyPayment(ctx.request.query);
      ctx.redirect(res.redirect_url);
    } catch (err) {
      ctx.body = err;
    }
  },
  getPageData: async (ctx, next) => {
    const res = await strapi.service('api::som-api.som-api').getPageData();
    if (!res || !res.ok) {
      ctx.body = res.error;
      ctx.status = 400;
      return;
    }
    ctx.body = res.data;
  },
  getAllSessions: async (ctx, next) => {
    const res = await strapi.service('api::som-api.som-api').getAllSessions();
    if (!res || !res.ok) {
      ctx.body = res.error;
      ctx.status = 400;
      return;
    }
    ctx.body = res;
  },
  donateToSom: async (ctx, next) => {
    const res = await strapi.service('api::som-api.som-api').donateToSom(ctx.request.body);
    if (!res || !res.ok) {
      ctx.body = res.error;
      ctx.status = 400;
      return;
    }
    ctx.body = res;
  }
};
