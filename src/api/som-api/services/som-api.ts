// @ts-nocheck
/**
 * som-api service
 * @description: A set of functions similar to controller's actions to avoid code duplication.
 * Note: responses contain 'ok' and one of 'data' or 'error' properties, data and error are not mutually exclusive.
 */
import { randomUUID } from "crypto";
const Flutterwave = require('flutterwave-node-v3');
const flw = new Flutterwave(process.env.SOM_FLW_PUBLIC_KEY, process.env.SOM_FLW_SECRET_KEY);

const getReference = (prefix = "SOM-TUTN") => [prefix, randomUUID()].join("-");
const getCode = () => {
  const code = (""+Math.random()).substring(2,7);
  return `SOM-${code}`;
}
const sum = (acc, curr) => acc + curr.amount;

export default () => ({
  getCurrentSession: async () => {
    const session = await strapi.documents(`api::som-session.som-session`).findMany({
      sort: "start_date:desc",
    });
    return session[0];
  },
  getAllSessions: async () => {
    const prev_sessions = [{session: "Aug/Oct 2022"}, {session: "May/Aug 2022"}, {session: "March 2023 BMC"}, {session: "BMC Cohort 2"}]
    const sessions = await strapi.documents(`api::som-session.som-session`).findMany({
      sort: "createdAt:asc",
    });
    {
      return {
        ok: true,
        message: "Sessions fetched successfully",
        sessions: prev_sessions.concat(sessions),
      };}
  },
  validateRegistrationData: async (data) => {
    const { email, surname, otherNames, phoneNumber, address, occupation, photoUrl } = data;
    if (! email || ! surname || ! otherNames || ! phoneNumber || ! address || ! occupation || ! photoUrl) {
      return {
        ok: false,
        error: {
          error: 'All fields are required: email, surname, otherNames, phoneNumber, address, occupation, photoUrl',
          meta: {
            type: "Validation",
          },
        },
      };
    }
    return {
      ok: true,
      data
    };
  },
  register: async (data, session) => {
    const code = getCode();
      const {
        email,
        surname,
        otherNames: other_names,
        phoneNumber: phone_number,
        address,
        occupation,
        nationality,
        salvationStory: salvation_story,
        call,
        ministryExperience: ministry_experience,
        isPetraMember: is_petra_member,
        commitmentToPetra: commitment_to_petra,
        maritalStatus: marital_status,
        whoIsPastorAyo: who_is_pastor_ayo_to_you,
        dob,
        joinedPetraAt: joined_petra_at,
        joinedPetra: joined_petra,
        weddingAnniversary: wedding_anniversary,
        noOfChildren: no_of_children,
        photoUrl: photo_url,
        howDidYouFindOut: how_did_you_find_out,
        ministry,
        positionInMinistry: position_in_ministry
      } = data;

      const fees = await strapi.documents(`api::som-fee.som-fee`).findMany({
        filters: {
          session: session,
          name: "Tuition",
        }
      });

      try {
        const reg_data = {
          who_is_pastor_ayo_to_you: who_is_pastor_ayo_to_you ?? '-',
          dob: new Date(dob),
          joined_petra_at: joined_petra_at ?? null,
          wedding_anniversary: wedding_anniversary ?? null,
          no_of_children: no_of_children ? no_of_children : 0,
          email,
          code,
          surname,
          other_names,
          phone_number,
          address,
          occupation,
          nationality,
          salvation_story,
          call,
          ministry_experience,
          is_petra_member,
          commitment_to_petra,
          marital_status,
          joined_petra,
          photo_url,
          how_did_you_find_out: how_did_you_find_out ?? '-',
          session,
          ministry,
          position_in_ministry,
        };
        const registration = await strapi.documents(`api::som-registration.som-registration`).create({
          data: reg_data,
        });
        const fee = fees[0];

      let payments = [];
      for (let i = 1; i <= fee.installments; i++) {
        const dueAt = new Date();
        // add i-1 months to the due date
        dueAt.setMonth(dueAt.getMonth() + (i - 1));
        payments.push({
          payer_id: registration.id,
          amount: fee.amount / fee.installments,
          dueAt: dueAt,
          reference: getReference(),
          description: [fee.description, fee.installments > 1 ? `Installment ${i}` : ""].join(" "),
          currency: fee.currency
        });
        // insert each payment in the payments array
        payments.forEach(async (payment) => {
          await strapi.documents(`api::som-payment.som-payment`).create({
            data: payment,
          });
        });
      }
      return {
        ok: true,
        data: {
          message: `Success`,
          data: {
            code,
            email,
            amount: fee.amount,
            installments: fee.installments,
            currency: fee.currency,
          },
        },
      };
      } catch (error) {
        console.error(error);
        const isUniqueError = error.message?.includes('unique')

        const message = isUniqueError ?
          'It seems you have already registered with these details, please contact us to process your registration' :
          "Unable to process your registration, if you have registered before please proceed to make payments using the link"

        return {
          ok: false,
          error: {
            error: message,
            meta: {
              type: "Duplicate",
            },
          },
        };
      }
  },
  sendRegistrationEmail: async (data, session, start_date, end_date, contact = '***********', location = 'Lagos, Nigeria') => {
    const { email, code, amount, currency } = data;
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const formattedStartDate = startDate.toLocaleDateString('en-GB', options);
    const formattedEndDate = endDate.toLocaleDateString('en-GB', options);

    const accountDetailsNigeria = `
      Account details:<br />
      **********<br />
      PETRA LEADERSHIP COLLEGE<br />
      Guarantee Trust Bank<br />
      <br />
    `;
    const accountDetailsUK = `
      Account details:<br />
      ********<br />
      Petra Christian Centre Ltd<br />
      Sort code: 30-99-50<br />
      <br />
    `;
    const accountDetails = location.includes('UK') ? accountDetailsUK : accountDetailsNigeria;

    const email_content = `<div><p>Congratulations, you have successfully registered for the ${session} of the Basic Ministry Course at Tribe Petra School of Ministry which will be held from ${formattedStartDate} TO ${formattedEndDate}, 8AM to 5PM daily (Mondays to Fridays only). </p><p>Your student code is <b>${code}</b> and you can use this code to confirm or make payment at ${process.env.SOM_FRONTEND_URL}/som/payment.</p>
    <p>An alternative way to make payment is by making a direct transfer to the account below and sending your proof of payment and registered name on the SOM platform via WhatsApp to ${contact}.</p><br />
    ${accountDetails}
    <br />
    FEE: ${currency} ${amount}<br />
    <br />
    <br />
    <p>For help, kindly call ${contact}.</p>
    <br />
    <p>Thank you once again for joining us in this journey to find purpose.</p>
    </div>`;

    await strapi.plugins['email'].services.email.send({
      to: email,
      from: '',
      subject: `Welcome to Tribe Petra School of Ministry`,
      text: email_content,
      html: `${email_content}`,
    });
  },
  sendPaymentEmail: async (registration, group_chat_link) => {
    await strapi.plugins['email'].services.email.send({
          to: registration.email,
          from: '',
          subject: `Payment Confirmation for Tribe Petra School of Ministry`,
          text: `Dear ${registration.surname}

          Thank you for making your payment for the Tribe Petra School of Ministry.

          Your payment has been received and confirmed.

          Your student code is ${registration.code}. You can use this code for any future references.

          Kindly use the link below to join your cohort Whatsapp group. Venue for lectures and other important information will be communicated in the group.

          ${group_chat_link}

          Thank you for your commitment and we look forward to seeing you at the training.

          Best regards,
          Tribe Petra School of Ministry.
          `,
          html: `<div>
            <p>Dear ${registration.surname},</p>
            <p>Thank you for making your payment for the Tribe Petra School of Ministry. </p>
            <p>Your payment has been received and confirmed.</p>
            <p>Your student code is <b>${registration.code}</b>. You can use this code for any future references.</p>
            <p>Kindly use the link below to join your cohort Whatsapp group. Venue for lectures and other important information will be communicated in the group.</p>
            <p>${group_chat_link}</p>
            <p>Thank you for your commitment and we look forward to seeing you at the training.</p>
            <p>Best regards,</p>
            <p>Tribe Petra School of Ministry</p>
          </div>`,
        });
  },
  getOutstandingPayments: async (email, code) => {
    const records = await strapi.documents(`api::som-registration.som-registration`).findMany({
      filters: {
        email,
        code,
      },
    });

    if (!records.length) {
      return {
        ok: false,
        error: {
          error: `No registration found with the provided details`,
          meta: {
            type: "NotFound",
          },
        },
      };
    }
    const registration = records[0];

    const payments = await strapi.documents(`api::som-payment.som-payment`).findMany({
      filters: {
        payer_id: registration.id,
        payment_status: "pending",
        payer_type: "registration",
      },
    });

    const transactions = await strapi.documents(`api::som-transaction.som-transaction`).findMany({
      filters: {
        payer_id: registration.id,
      },
    });

    const { surname, other_names: otherNames, phone_number: phoneNumber } = registration;

    const outstandingPayments = await Promise.all(payments.map(async (payment) => {
      const rebates = await strapi.documents(`api::rebate.rebate`).findMany({
        filters: {
          payment_id: payment.id,
        },
      });
      const { due_at: dueAt, amount, amount_paid: amountPaid } = payment;
      const totalRebates = rebates.length ? rebates.reduce(sum, 0) : 0;
      const outstandingAmount = amount - totalRebates - amountPaid;

      return {
        ...payment,
        amount: outstandingAmount,
        dueAt: dueAt ? dueAt.toISOString() : null,
        amountPaid: amountPaid,
      };
    }))
    return {
      ok: true,
      data: {
        message: `Success`,
        data: {
          candidateData: { surname, otherNames, phoneNumber },
          outstandingPayments,
          transactions: transactions.map((t) => ({
            amount: t.amount,
            id: Number(t.id),
            createdAt: t.createdAt,
            description: t.description,
            channel: t.channel,
          })),
        }
      }
    };
  },
  handleVerifyPayment: async (query) => {
    const redirectUrl = `${process.env.SOM_FRONTEND_URL}/som/payment`;
    const payments = await strapi.documents(`api::som-payment.som-payment`).findMany({
      filters: {
        reference: query.tx_ref,
      },
    });
    if (!payments.length) {
      return {
        message: 'Invalid transaction reference',
        ok: false,
        redirect_url: redirectUrl,
      };
    }
    const payment = payments[0];
    const rebates = await strapi.documents(`api::rebate.rebate`).findMany({
      filters: {
        payment_id: payment.id,
      },
    });
    // sum up all rebates
    const totalRebates = rebates.length ? rebates.reduce(sum, 0) : 0;

    payment.amount -= totalRebates + payment.amount_paid;

    if (!payment?.amount) {
      return {
        message: 'Payment already completed',
        ok: false,
        redirect_url: redirectUrl,
      };
    }
    if (query.status === 'successful') {
      const response = await flw.Transaction.verify({id: query.transaction_id});
      if (
        response.data.status === "successful" &&
        response.data.charged_amount >= payment.amount &&
        response.data.currency === payment.currency
      ) {
        await strapi.documents(`api::som-payment.som-payment`).update({
          documentId: payment.documentId,
          data: {
            payment_status: "success",
            amount_paid: payment.amount_paid + response.data.charged_amount,
          }
        });
        await strapi.documents(`api::som-transaction.som-transaction`).create({
          data: {
            amount: response.data.charged_amount,
            currency: response.data.currency,
            channel: response.data.payment_type,
            status: "success",
            meta: response.data,
            payer_id: payment.payer_id,
            payment_id: payment.id,
            description: payment.description,
          },
        });

        const registrations = await strapi.documents(`api::som-registration.som-registration`).findMany({
          filters: {
            id: payment.payer_id,
          },
          fields: ['email', 'code', 'surname', 'other_names', 'phone_number']
        });
        const registration = registrations[0];
        let message;
        if (payment.payer_type === "registration") {
          message = `Payment successful, thank you for your payment`;
          // send payment confirmation email
          const { group_chat_link } = await strapi.services['som-api'].getCurrentSession();
          await strapi.service('api::som-api.som-api').sendPaymentEmail(registration, group_chat_link);
        } else {
          message = `Payment successful, thank you for your donation`;
        }

        const { email, code } = registration;
        return {
          message: 'Redirecting to frontend',
          ok: true,
          redirect_url: `${redirectUrl}?email=${email}&code=${code}&message=${message}`,
        };
      } else {
        return {
          message: 'Payment verification failed',
          ok: false,
          redirect_url: redirectUrl,
        }
      }
    }
  },
  getPageData: async () => {
    const sessions = await strapi.documents(`api::som-session.som-session`).findMany({
      sort: "start_date:desc",
      fields: ['session', 'can_register']
    });
    const session = sessions[0];
    const fees = await strapi.documents(`api::som-fee.som-fee`).findMany({
      sort: "createdAt:desc",
      filters: {
        session: session.session,
        name: "Tuition",
      }
    });
    const fee = fees[0];
    return {
      ok: true,
      data: {
        message: `Success`,
        data: {
          session: session,
          fee: fee,
          name: 'The Tribe Petra School of Ministry Name',
          id: 'ef6f2c49-8e41-4dd8-bddf-5cfca8b8a98c',
          slug: 'tmi',
          title: 'Tribe Petra School of Ministry',
          thumbnail: 'https://res.cloudinary.com/dazocl67q/image/upload/v1673121841/ayoajani.com/tmi-cover_d9x6vt.jpg',
          description: [
            {
              text: 'The Tribe Petra School of Ministry is an intensive training program designed to equip and empower believers to discover and fulfill their God-given purpose.',
            },
            {
              text: 'This Basic Ministry Course is a two-week, on-site training for full-time and part-time Ministers, Leads of Para Ministries and all gospel workers.',
            },
            {
              text: `To complete your registration you would be required to pay a tuition fee of ${fee.fee_text_display} only, which covers the cost of your tuition for the duration of the training.`,
            },
          ]
        }
      }
    }
  },
  donateToSom: async (data) => {
    const { email, amount, currency, description, payer_id, payer_type } = data;
    const reference = getReference("SOM-DONATE");
    const payment = await strapi.documents(`api::som-payment.som-payment`).create({
      data: {
        payer_id,
        payer_type,
        amount,
        currency,
        reference,
        description,
        payment_status: "pending",
      }
    });

    return {
      ok: true,
      data: {
        message: `Success`,
        payment,
      }
    };
  }
});
