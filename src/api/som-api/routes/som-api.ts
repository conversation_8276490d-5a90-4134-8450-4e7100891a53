// @ts-nocheck
export default {
  routes: [
    {
     method: 'POST',
     path: '/som-api/register',
     handler: 'som-api.register',
     config: {
       auth: false,
       policies: [],
       middlewares: [],
     },
    },
    {
      method: 'GET',
      path: '/som-api/get-outstanding-payments',
      handler: 'som-api.getOutstandingPayments',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
     },
     {
      method: 'GET',
      path: '/som-api/flw-redirect',
      handler: 'som-api.handleVerifyPayment',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
     },
     {
      method: 'GET',
      path: '/som-api/get-page-data',
      handler: 'som-api.getPageData',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
     },
     {
      method: 'GET',
      path: '/som-api/get-all-sessions',
      handler: 'som-api.getAllSessions',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
     {
      method: 'POST',
      path: '/som-api/donate-to-som',
      handler: 'som-api.donateToSom',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
  ],
};
