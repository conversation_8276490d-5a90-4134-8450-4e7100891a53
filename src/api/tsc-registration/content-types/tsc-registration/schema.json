{"kind": "collectionType", "collectionName": "tsc_registrations", "info": {"singularName": "tsc-registration", "pluralName": "tsc-registrations", "displayName": "TSC Registration", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"row_number": {"type": "integer"}, "spreadsheet_id": {"type": "string"}, "sheet": {"type": "string"}, "full_name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "email"}, "gender": {"type": "string"}, "age_range": {"type": "string"}, "marital_status": {"type": "string"}, "physical_attendance": {"type": "string"}, "petra_member": {"type": "string"}, "how_did_you_hear": {"type": "string"}, "ig_handle": {"type": "string"}, "expectations": {"type": "text"}, "timestamp": {"type": "string"}, "qrcode_email_sent": {"type": "boolean", "default": false}}}