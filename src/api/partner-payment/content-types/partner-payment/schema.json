{"kind": "collectionType", "collectionName": "partner_payments", "info": {"singularName": "partner-payment", "pluralName": "partner-payments", "displayName": "Partner Payment", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"partner_code": {"type": "string"}, "payment_date": {"type": "date"}, "flw_payload": {"type": "text"}, "currency": {"type": "string"}, "status": {"type": "enumeration", "enum": ["pending", "successful", "failed"], "default": "pending"}, "period": {"type": "enumeration", "enum": ["weekly", "monthly", "quarterly", "yearly", "one-time"]}, "period_start": {"type": "date"}, "period_end": {"type": "date"}, "txn_ref": {"type": "string"}, "payment_link": {"type": "string"}, "description": {"type": "string"}, "amount": {"type": "decimal"}, "offline_payment_status": {"type": "enumeration", "enum": ["pending", "awaiting confirmation", "successful"], "default": "pending"}}}