{"kind": "collectionType", "collectionName": "church_members", "info": {"singularName": "church-member", "pluralName": "church-members", "displayName": "Church Member", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"username": {"type": "uid", "required": false}, "firstname": {"type": "string"}, "surname": {"type": "string", "required": true}, "email": {"type": "email", "unique": true}, "middlename": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "is_married": {"type": "boolean"}, "is_worker": {"type": "boolean"}, "department": {"type": "string"}, "position": {"type": "enumeration", "enum": ["Team Lead", "Assistant Team Lead", "Member"], "default": "Member"}, "attendance_sheets": {"type": "relation", "relation": "oneToMany", "target": "api::attendance-sheet.attendance-sheet", "mappedBy": "member_id"}, "uuid": {"type": "uid", "required": true}, "phone": {"type": "text"}}}