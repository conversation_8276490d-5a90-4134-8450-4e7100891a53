{"kind": "collectionType", "collectionName": "event_attendances", "info": {"singularName": "event-attendance", "pluralName": "event-attendances", "displayName": "Event Attendance", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"event": {"type": "relation", "relation": "manyToOne", "target": "api::event.event", "inversedBy": "event_attendances"}, "event_attendee": {"type": "relation", "relation": "oneToMany", "target": "api::event-attendee.event-attendee"}, "event_session": {"type": "relation", "relation": "oneToOne", "target": "api::event-session.event-session"}, "attendance_mode": {"pluginOptions": {"i18n": {"localized": true}}, "type": "enumeration", "enum": ["In-Person", "Online", "Hybrid"], "default": "In-Person"}, "how_did_you_hear": {"pluginOptions": {"i18n": {"localized": true}}, "type": "enumeration", "enum": ["YouTube", "Instagram", "Facebook", "Blogger", "Friend/Family", "Church Announcement"]}}}