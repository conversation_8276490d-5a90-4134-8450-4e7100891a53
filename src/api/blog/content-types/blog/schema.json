{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "Blog", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "content": {"pluginOptions": {"i18n": {"localized": true}}, "type": "richtext"}, "author": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "excerpt": {"pluginOptions": {"i18n": {"localized": true}}, "type": "text"}, "category": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "images": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "banner": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "slug": {"pluginOptions": {"i18n": {"localized": true}}, "type": "uid", "targetField": "title"}}}