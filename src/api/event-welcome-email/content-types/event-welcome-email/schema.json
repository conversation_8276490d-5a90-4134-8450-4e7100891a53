{"kind": "collectionType", "collectionName": "event_welcome_emails", "info": {"singularName": "event-welcome-email", "pluralName": "event-welcome-emails", "displayName": "Event Welcome Email"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"attendee": {"type": "relation", "relation": "oneToOne", "target": "api::event-attendee.event-attendee"}, "event": {"type": "relation", "relation": "oneToOne", "target": "api::event.event"}, "email_sent": {"type": "boolean"}}}