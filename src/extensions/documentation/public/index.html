<!-- HTML for static distribution bundle build --><!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Swagger UI</title>
    <link
      rel="stylesheet"
      type="text/css"
      href="/plugins/documentation/swagger-ui.css"
    />
    <link
      rel="icon"
      type="image/png"
      href="/plugins/documentation/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="/plugins/documentation/favicon-16x16.png"
      sizes="16x16"
    />
    <style>
      html {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after {
        box-sizing: inherit;
      }

      body {
        margin: 0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script class="custom-swagger-ui">
      window.onload = function() {
        const ui = SwaggerUIBundle({
          url: "https://petstore.swagger.io/v2/swagger.json",
          spec: {"openapi":"3.0.0","info":{"version":"1.0.0","title":"DOCUMENTATION","description":"","termsOfService":"YOUR_TERMS_OF_SERVICE_URL","contact":{"name":"TEAM","email":"<EMAIL>","url":"mywebsite.io"},"license":{"name":"Apache 2.0","url":"https://www.apache.org/licenses/LICENSE-2.0.html"},"x-generation-date":"2024-05-22T13:12:12.336Z"},"x-strapi-config":{"path":"/documentation","plugins":["upload","users-permissions"]},"servers":[{"url":"http://localhost:1337/api","description":"Development server"}],"externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html"},"security":[{"bearerAuth":[]}],"components":{"securitySchemes":{"bearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"}},"schemas":{"Error":{"type":"object","required":["error"],"properties":{"data":{"nullable":true,"oneOf":[{"type":"object"},{"type":"array","items":{"type":"object"}}]},"error":{"type":"object","properties":{"status":{"type":"integer"},"name":{"type":"string"},"message":{"type":"string"},"details":{"type":"object"}}}}},"AttendanceSheetRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["uuid"],"type":"object","properties":{"programme_name":{"type":"string"},"programme_id":{"type":"string"},"member_id":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"uuid":{"type":"string"}}}}},"AttendanceSheetListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/AttendanceSheet"}}},"AttendanceSheetListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/AttendanceSheetListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"AttendanceSheet":{"type":"object","required":["uuid"],"properties":{"programme_name":{"type":"string"},"programme_id":{"type":"string"},"member_id":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"username":{"type":"string"},"firstname":{"type":"string"},"surname":{"type":"string"},"email":{"type":"string","format":"email"},"middlename":{"type":"string"},"address":{"type":"string"},"city":{"type":"string"},"is_married":{"type":"boolean"},"is_worker":{"type":"boolean"},"department":{"type":"string"},"position":{"type":"string","enum":["Team Lead","Assistant Team Lead","Member"]},"attendance_sheets":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"programme_name":{"type":"string"},"programme_id":{"type":"string"},"member_id":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"uuid":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"uuid":{"type":"string"},"phone":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"uuid":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"AttendanceSheetResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/AttendanceSheet"}}},"AttendanceSheetResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/AttendanceSheetResponseDataObject"},"meta":{"type":"object"}}},"BlogLocalizationRequest":{"required":["locale"],"type":"object","properties":{"title":{"type":"string"},"content":{"type":"string"},"author":{"type":"string"},"excerpt":{"type":"string"},"category":{"type":"string"},"images":{"type":"string"},"banner":{"type":"string"},"slug":{"type":"string"},"locale":{"type":"string"}}},"BlogRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"title":{"type":"string"},"content":{"type":"string"},"author":{"type":"string"},"excerpt":{"type":"string"},"category":{"type":"string"},"images":{"type":"string"},"banner":{"type":"string"},"slug":{"type":"string"},"locale":{"type":"string"}}}}},"BlogResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Blog"}}},"BlogLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/BlogResponseDataObjectLocalized"},"meta":{"type":"object"}}},"BlogListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Blog"}}},"BlogLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/BlogListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"BlogListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Blog"}}},"BlogListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/BlogListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Blog":{"type":"object","properties":{"title":{"type":"string"},"content":{"type":"string"},"author":{"type":"string"},"excerpt":{"type":"string"},"category":{"type":"string"},"images":{"type":"string"},"banner":{"type":"string"},"slug":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Blog"}}}},"locale":{"type":"string"}}},"BlogResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Blog"}}},"BlogResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/BlogResponseDataObject"},"meta":{"type":"object"}}},"CareGroupRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"zone":{"type":"string"},"location":{"type":"string"},"coordinator":{"type":"string"},"phone":{"type":"string"},"cells":{},"order":{"type":"integer"}}}}},"CareGroupListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/CareGroup"}}},"CareGroupListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/CareGroupListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"CareGroup":{"type":"object","properties":{"zone":{"type":"string"},"location":{"type":"string"},"coordinator":{"type":"string"},"phone":{"type":"string"},"cells":{},"order":{"type":"integer"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"CareGroupResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/CareGroup"}}},"CareGroupResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/CareGroupResponseDataObject"},"meta":{"type":"object"}}},"ChurchMemberRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["surname","uuid"],"type":"object","properties":{"username":{"type":"string"},"firstname":{"type":"string"},"surname":{"type":"string"},"email":{"type":"string","format":"email"},"middlename":{"type":"string"},"address":{"type":"string"},"city":{"type":"string"},"is_married":{"type":"boolean"},"is_worker":{"type":"boolean"},"department":{"type":"string"},"position":{"type":"string","enum":["Team Lead","Assistant Team Lead","Member"]},"attendance_sheets":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"uuid":{"type":"string"},"phone":{"type":"string"}}}}},"ChurchMemberListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/ChurchMember"}}},"ChurchMemberListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ChurchMemberListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"ChurchMember":{"type":"object","required":["surname","uuid"],"properties":{"username":{"type":"string"},"firstname":{"type":"string"},"surname":{"type":"string"},"email":{"type":"string","format":"email"},"middlename":{"type":"string"},"address":{"type":"string"},"city":{"type":"string"},"is_married":{"type":"boolean"},"is_worker":{"type":"boolean"},"department":{"type":"string"},"position":{"type":"string","enum":["Team Lead","Assistant Team Lead","Member"]},"attendance_sheets":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"programme_name":{"type":"string"},"programme_id":{"type":"string"},"member_id":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"username":{"type":"string"},"firstname":{"type":"string"},"surname":{"type":"string"},"email":{"type":"string","format":"email"},"middlename":{"type":"string"},"address":{"type":"string"},"city":{"type":"string"},"is_married":{"type":"boolean"},"is_worker":{"type":"boolean"},"department":{"type":"string"},"position":{"type":"string","enum":["Team Lead","Assistant Team Lead","Member"]},"attendance_sheets":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"uuid":{"type":"string"},"phone":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"uuid":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"uuid":{"type":"string"},"phone":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"ChurchMemberResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/ChurchMember"}}},"ChurchMemberResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ChurchMemberResponseDataObject"},"meta":{"type":"object"}}},"ChurchServiceRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["uuid"],"type":"object","properties":{"uuid":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"start_time":{"type":"string","format":"date-time"},"banner":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"schedule":{"type":"string"},"duration":{"type":"integer"}}}}},"ChurchServiceListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/ChurchService"}}},"ChurchServiceListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ChurchServiceListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"ChurchService":{"type":"object","required":["uuid"],"properties":{"uuid":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"start_time":{"type":"string","format":"date-time"},"banner":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"folder":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"children":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"files":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"folder":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"schedule":{"type":"string"},"duration":{"type":"integer"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"ChurchServiceResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/ChurchService"}}},"ChurchServiceResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ChurchServiceResponseDataObject"},"meta":{"type":"object"}}},"ConvertRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string"},"address":{"type":"string"},"gender":{"type":"string","enum":["Male","Female","male","female"]},"marital_status":{"type":"string","enum":["single","married","separated","divorced"]},"prayer_request":{"type":"string"},"converted_by":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"ministry_materials":{"type":"string"}}}}},"ConvertListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Convert"}}},"ConvertListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ConvertListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Convert":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string"},"address":{"type":"string"},"gender":{"type":"string","enum":["Male","Female","male","female"]},"marital_status":{"type":"string","enum":["single","married","separated","divorced"]},"prayer_request":{"type":"string"},"converted_by":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"converts":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string"},"address":{"type":"string"},"gender":{"type":"string","enum":["Male","Female","male","female"]},"marital_status":{"type":"string","enum":["single","married","separated","divorced"]},"prayer_request":{"type":"string"},"converted_by":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"ministry_materials":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"ministry_materials":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"ConvertResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Convert"}}},"ConvertResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ConvertResponseDataObject"},"meta":{"type":"object"}}},"DirectorateRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"description":{"type":"string"},"lead":{"type":"string"},"departments":{},"name":{"type":"string"}}}}},"DirectorateListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Directorate"}}},"DirectorateListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/DirectorateListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Directorate":{"type":"object","properties":{"description":{"type":"string"},"lead":{"type":"string"},"departments":{},"name":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"DirectorateResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Directorate"}}},"DirectorateResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/DirectorateResponseDataObject"},"meta":{"type":"object"}}},"EventLocalizationRequest":{"required":["locale"],"type":"object","properties":{"title":{"type":"string"},"slug":{"type":"string"},"start_date":{"type":"string","format":"date-time"},"end_date":{"type":"string","format":"date-time"},"schedule":{"type":"string"},"description":{"type":"string"},"organiser":{"type":"string"},"must_register":{"type":"boolean"},"venue":{"type":"string"},"address":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"website":{"type":"string"},"event_sessions":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"event_attendances":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"image":{"type":"string"},"current_session":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"reg_info":{"type":"string"},"locale":{"type":"string"}}},"EventRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"title":{"type":"string"},"slug":{"type":"string"},"start_date":{"type":"string","format":"date-time"},"end_date":{"type":"string","format":"date-time"},"schedule":{"type":"string"},"description":{"type":"string"},"organiser":{"type":"string"},"must_register":{"type":"boolean"},"venue":{"type":"string"},"address":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"website":{"type":"string"},"event_sessions":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"event_attendances":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"image":{"type":"string"},"current_session":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"reg_info":{"type":"string"},"locale":{"type":"string"}}}}},"EventResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Event"}}},"EventLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventResponseDataObjectLocalized"},"meta":{"type":"object"}}},"EventListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Event"}}},"EventLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Event"}}},"EventListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Event":{"type":"object","properties":{"title":{"type":"string"},"slug":{"type":"string"},"start_date":{"type":"string","format":"date-time"},"end_date":{"type":"string","format":"date-time"},"schedule":{"type":"string"},"description":{"type":"string"},"organiser":{"type":"string"},"must_register":{"type":"boolean"},"venue":{"type":"string"},"address":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"website":{"type":"string"},"event_sessions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"title":{"type":"string"},"date_time":{"type":"string","format":"date-time"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"event_attendances":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"event":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"title":{"type":"string"},"slug":{"type":"string"},"start_date":{"type":"string","format":"date-time"},"end_date":{"type":"string","format":"date-time"},"schedule":{"type":"string"},"description":{"type":"string"},"organiser":{"type":"string"},"must_register":{"type":"boolean"},"venue":{"type":"string"},"address":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"website":{"type":"string"},"event_sessions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"event_attendances":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"image":{"type":"string"},"current_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"reg_info":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}},"event_attendee":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"gender":{"type":"string","enum":["Male","Female"]},"location":{"type":"string"},"country":{"type":"string"},"is_petra_member":{"type":"boolean"},"is_petra_worker":{"type":"boolean"},"is_minister":{"type":"boolean"},"ministry_name":{"type":"string"},"ministry_location":{"type":"string"},"ministry_position":{"type":"string"},"is_first_timer":{"type":"boolean"},"petra_department":{"type":"string"},"is_student":{"type":"boolean"},"school_name":{"type":"string"},"level_in_school":{"type":"string"},"attendance_mode":{"type":"string","enum":["Online","In-Person","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"event_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"attendance_mode":{"type":"string","enum":["In-Person","Online","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"image":{"type":"string"},"current_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"reg_info":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Event"}}}},"locale":{"type":"string"}}},"EventResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Event"}}},"EventResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventResponseDataObject"},"meta":{"type":"object"}}},"EventAttendanceLocalizationRequest":{"required":["locale"],"type":"object","properties":{"event":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"event_attendee":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"event_session":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"attendance_mode":{"type":"string","enum":["In-Person","Online","Hybrid"]},"locale":{"type":"string"}}},"EventAttendanceRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"event":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"event_attendee":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"event_session":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"attendance_mode":{"type":"string","enum":["In-Person","Online","Hybrid"]},"locale":{"type":"string"}}}}},"EventAttendanceResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendance"}}},"EventAttendanceLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventAttendanceResponseDataObjectLocalized"},"meta":{"type":"object"}}},"EventAttendanceListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendance"}}},"EventAttendanceLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventAttendanceListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventAttendanceListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendance"}}},"EventAttendanceListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventAttendanceListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventAttendance":{"type":"object","properties":{"event":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"title":{"type":"string"},"slug":{"type":"string"},"start_date":{"type":"string","format":"date-time"},"end_date":{"type":"string","format":"date-time"},"schedule":{"type":"string"},"description":{"type":"string"},"organiser":{"type":"string"},"must_register":{"type":"boolean"},"venue":{"type":"string"},"address":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"website":{"type":"string"},"event_sessions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"title":{"type":"string"},"date_time":{"type":"string","format":"date-time"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"event_attendances":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"event":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"event_attendee":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"gender":{"type":"string","enum":["Male","Female"]},"location":{"type":"string"},"country":{"type":"string"},"is_petra_member":{"type":"boolean"},"is_petra_worker":{"type":"boolean"},"is_minister":{"type":"boolean"},"ministry_name":{"type":"string"},"ministry_location":{"type":"string"},"ministry_position":{"type":"string"},"is_first_timer":{"type":"boolean"},"petra_department":{"type":"string"},"is_student":{"type":"boolean"},"school_name":{"type":"string"},"level_in_school":{"type":"string"},"attendance_mode":{"type":"string","enum":["Online","In-Person","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"event_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"attendance_mode":{"type":"string","enum":["In-Person","Online","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"image":{"type":"string"},"current_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"reg_info":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}},"event_attendee":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"event_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"attendance_mode":{"type":"string","enum":["In-Person","Online","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventAttendance"}}}},"locale":{"type":"string"}}},"EventAttendanceResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendance"}}},"EventAttendanceResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventAttendanceResponseDataObject"},"meta":{"type":"object"}}},"EventAttendeeLocalizationRequest":{"required":["locale"],"type":"object","properties":{"name":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"gender":{"type":"string","enum":["Male","Female"]},"location":{"type":"string"},"country":{"type":"string"},"is_petra_member":{"type":"boolean"},"is_petra_worker":{"type":"boolean"},"is_minister":{"type":"boolean"},"ministry_name":{"type":"string"},"ministry_location":{"type":"string"},"ministry_position":{"type":"string"},"is_first_timer":{"type":"boolean"},"petra_department":{"type":"string"},"is_student":{"type":"boolean"},"school_name":{"type":"string"},"level_in_school":{"type":"string"},"attendance_mode":{"type":"string","enum":["Online","In-Person","Hybrid"]},"locale":{"type":"string"}}},"EventAttendeeRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"gender":{"type":"string","enum":["Male","Female"]},"location":{"type":"string"},"country":{"type":"string"},"is_petra_member":{"type":"boolean"},"is_petra_worker":{"type":"boolean"},"is_minister":{"type":"boolean"},"ministry_name":{"type":"string"},"ministry_location":{"type":"string"},"ministry_position":{"type":"string"},"is_first_timer":{"type":"boolean"},"petra_department":{"type":"string"},"is_student":{"type":"boolean"},"school_name":{"type":"string"},"level_in_school":{"type":"string"},"attendance_mode":{"type":"string","enum":["Online","In-Person","Hybrid"]},"locale":{"type":"string"}}}}},"EventAttendeeResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendee"}}},"EventAttendeeLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventAttendeeResponseDataObjectLocalized"},"meta":{"type":"object"}}},"EventAttendeeListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendee"}}},"EventAttendeeLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventAttendeeListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventAttendeeListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendee"}}},"EventAttendeeListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventAttendeeListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventAttendee":{"type":"object","properties":{"name":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"gender":{"type":"string","enum":["Male","Female"]},"location":{"type":"string"},"country":{"type":"string"},"is_petra_member":{"type":"boolean"},"is_petra_worker":{"type":"boolean"},"is_minister":{"type":"boolean"},"ministry_name":{"type":"string"},"ministry_location":{"type":"string"},"ministry_position":{"type":"string"},"is_first_timer":{"type":"boolean"},"petra_department":{"type":"string"},"is_student":{"type":"boolean"},"school_name":{"type":"string"},"level_in_school":{"type":"string"},"attendance_mode":{"type":"string","enum":["Online","In-Person","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventAttendee"}}}},"locale":{"type":"string"}}},"EventAttendeeResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventAttendee"}}},"EventAttendeeResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventAttendeeResponseDataObject"},"meta":{"type":"object"}}},"EventRegistrationLocalizationRequest":{"required":["locale"],"type":"object","properties":{"event":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"event_attendee":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"}}},"EventRegistrationRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"event":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"event_attendee":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"}}}}},"EventRegistrationResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventRegistration"}}},"EventRegistrationLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventRegistrationResponseDataObjectLocalized"},"meta":{"type":"object"}}},"EventRegistrationListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventRegistration"}}},"EventRegistrationLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventRegistrationListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventRegistrationListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventRegistration"}}},"EventRegistrationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventRegistrationListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventRegistration":{"type":"object","properties":{"event":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"title":{"type":"string"},"slug":{"type":"string"},"start_date":{"type":"string","format":"date-time"},"end_date":{"type":"string","format":"date-time"},"schedule":{"type":"string"},"description":{"type":"string"},"organiser":{"type":"string"},"must_register":{"type":"boolean"},"venue":{"type":"string"},"address":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"website":{"type":"string"},"event_sessions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"title":{"type":"string"},"date_time":{"type":"string","format":"date-time"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"event_attendances":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"event":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"event_attendee":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"gender":{"type":"string","enum":["Male","Female"]},"location":{"type":"string"},"country":{"type":"string"},"is_petra_member":{"type":"boolean"},"is_petra_worker":{"type":"boolean"},"is_minister":{"type":"boolean"},"ministry_name":{"type":"string"},"ministry_location":{"type":"string"},"ministry_position":{"type":"string"},"is_first_timer":{"type":"boolean"},"petra_department":{"type":"string"},"is_student":{"type":"boolean"},"school_name":{"type":"string"},"level_in_school":{"type":"string"},"attendance_mode":{"type":"string","enum":["Online","In-Person","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"event_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"attendance_mode":{"type":"string","enum":["In-Person","Online","Hybrid"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}}},"image":{"type":"string"},"current_session":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"reg_info":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{}}}},"locale":{"type":"string"}}}}}}},"event_attendee":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventRegistration"}}}},"locale":{"type":"string"}}},"EventRegistrationResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventRegistration"}}},"EventRegistrationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventRegistrationResponseDataObject"},"meta":{"type":"object"}}},"EventSessionLocalizationRequest":{"required":["locale"],"type":"object","properties":{"title":{"type":"string"},"date_time":{"type":"string","format":"date-time"},"locale":{"type":"string"}}},"EventSessionRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"title":{"type":"string"},"date_time":{"type":"string","format":"date-time"},"locale":{"type":"string"}}}}},"EventSessionResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventSession"}}},"EventSessionLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventSessionResponseDataObjectLocalized"},"meta":{"type":"object"}}},"EventSessionListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventSession"}}},"EventSessionLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventSessionListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventSessionListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventSession"}}},"EventSessionListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventSessionListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"EventSession":{"type":"object","properties":{"title":{"type":"string"},"date_time":{"type":"string","format":"date-time"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/EventSession"}}}},"locale":{"type":"string"}}},"EventSessionResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/EventSession"}}},"EventSessionResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/EventSessionResponseDataObject"},"meta":{"type":"object"}}},"HeroItemLocalizationRequest":{"required":["locale"],"type":"object","properties":{"Title":{"type":"string"},"subtitle":{"type":"string"},"icon":{"type":"string"},"url":{"type":"string"},"description":{"type":"string"},"button_text":{"type":"string"},"route":{"type":"string"},"position":{"type":"string"},"status":{"type":"string","enum":["active","inactive"]},"banner":{"type":"string"},"ministers":{"type":"string"},"locale":{"type":"string"}}},"HeroItemRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"Title":{"type":"string"},"subtitle":{"type":"string"},"icon":{"type":"string"},"url":{"type":"string"},"description":{"type":"string"},"button_text":{"type":"string"},"route":{"type":"string"},"position":{"type":"string"},"status":{"type":"string","enum":["active","inactive"]},"banner":{"type":"string"},"ministers":{"type":"string"},"locale":{"type":"string"}}}}},"HeroItemResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/HeroItem"}}},"HeroItemLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/HeroItemResponseDataObjectLocalized"},"meta":{"type":"object"}}},"HeroItemListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/HeroItem"}}},"HeroItemLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/HeroItemListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"HeroItemListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/HeroItem"}}},"HeroItemListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/HeroItemListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"HeroItem":{"type":"object","properties":{"Title":{"type":"string"},"subtitle":{"type":"string"},"icon":{"type":"string"},"url":{"type":"string"},"description":{"type":"string"},"button_text":{"type":"string"},"route":{"type":"string"},"position":{"type":"string"},"status":{"type":"string","enum":["active","inactive"]},"banner":{"type":"string"},"ministers":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/HeroItem"}}}},"locale":{"type":"string"}}},"HeroItemResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/HeroItem"}}},"HeroItemResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/HeroItemResponseDataObject"},"meta":{"type":"object"}}},"InternationalDelegateRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"address":{"type":"string"},"city":{"type":"string"},"state":{"type":"string"},"postal_code":{"type":"string"},"country":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"organisation":{"type":"string"},"organisation_type":{"type":"string"},"petra_member":{"type":"boolean"},"need_accomodation":{"type":"boolean"},"event":{"type":"string"}}}}},"InternationalDelegateListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/InternationalDelegate"}}},"InternationalDelegateListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/InternationalDelegateListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"InternationalDelegate":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"address":{"type":"string"},"city":{"type":"string"},"state":{"type":"string"},"postal_code":{"type":"string"},"country":{"type":"string"},"phone":{"type":"string"},"email":{"type":"string","format":"email"},"organisation":{"type":"string"},"organisation_type":{"type":"string"},"petra_member":{"type":"boolean"},"need_accomodation":{"type":"boolean"},"event":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"InternationalDelegateResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/InternationalDelegate"}}},"InternationalDelegateResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/InternationalDelegateResponseDataObject"},"meta":{"type":"object"}}},"MenuItemLocalizationRequest":{"required":["locale"],"type":"object","properties":{"title":{"type":"string"},"text":{"type":"string"},"route":{"type":"string"},"url":{"type":"string"},"icon":{"type":"string"},"highlight":{"type":"boolean"},"children":{"type":"string"},"position":{"type":"integer"},"group":{"type":"string"},"locale":{"type":"string"}}},"MenuItemRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"title":{"type":"string"},"text":{"type":"string"},"route":{"type":"string"},"url":{"type":"string"},"icon":{"type":"string"},"highlight":{"type":"boolean"},"children":{"type":"string"},"position":{"type":"integer"},"group":{"type":"string"},"locale":{"type":"string"}}}}},"MenuItemResponseDataObjectLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/MenuItem"}}},"MenuItemLocalizationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/MenuItemResponseDataObjectLocalized"},"meta":{"type":"object"}}},"MenuItemListResponseDataItemLocalized":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/MenuItem"}}},"MenuItemLocalizationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/MenuItemListResponseDataItemLocalized"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"MenuItemListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/MenuItem"}}},"MenuItemListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/MenuItemListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"MenuItem":{"type":"object","properties":{"title":{"type":"string"},"text":{"type":"string"},"route":{"type":"string"},"url":{"type":"string"},"icon":{"type":"string"},"highlight":{"type":"boolean"},"children":{"type":"string"},"position":{"type":"integer"},"group":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"localizations":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/MenuItem"}}}},"locale":{"type":"string"}}},"MenuItemResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/MenuItem"}}},"MenuItemResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/MenuItemResponseDataObject"},"meta":{"type":"object"}}},"PartnerRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"partner_code":{"type":"string"},"email":{"type":"string","format":"email"},"phone":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"currency":{"type":"string","enum":["NGN","USD","GBP","EUR","GHS","CAD"]},"frequency":{"type":"string","enum":["weekly","monthly","quarterly","yearly"]},"amount":{"type":"number","format":"float"}}}}},"PartnerListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Partner"}}},"PartnerListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/PartnerListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Partner":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"partner_code":{"type":"string"},"email":{"type":"string","format":"email"},"phone":{"type":"string"},"city":{"type":"string"},"country":{"type":"string"},"currency":{"type":"string","enum":["NGN","USD","GBP","EUR","GHS","CAD"]},"frequency":{"type":"string","enum":["weekly","monthly","quarterly","yearly"]},"amount":{"type":"number","format":"float"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"PartnerResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Partner"}}},"PartnerResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/PartnerResponseDataObject"},"meta":{"type":"object"}}},"PartnerMessageRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"channel":{"type":"string"},"status":{"type":"string","enum":["success","pending","failed"]},"description":{"type":"string"},"recipient_id":{"type":"integer"},"message_type":{"type":"string"}}}}},"PartnerMessageListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/PartnerMessage"}}},"PartnerMessageListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/PartnerMessageListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"PartnerMessage":{"type":"object","properties":{"channel":{"type":"string"},"status":{"type":"string","enum":["success","pending","failed"]},"description":{"type":"string"},"recipient_id":{"type":"integer"},"message_type":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"PartnerMessageResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/PartnerMessage"}}},"PartnerMessageResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/PartnerMessageResponseDataObject"},"meta":{"type":"object"}}},"PartnerPaymentRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"partner_code":{"type":"string"},"payment_date":{"type":"string","format":"date"},"flw_payload":{"type":"string"},"currency":{"type":"string"},"status":{"type":"string","enum":["pending","successful","failed"]},"period":{"type":"string","enum":["weekly","monthly","quarterly","yearly","one-time"]},"period_start":{"type":"string","format":"date"},"period_end":{"type":"string","format":"date"},"txn_ref":{"type":"string"},"payment_link":{"type":"string"},"description":{"type":"string"},"amount":{"type":"number","format":"float"},"offline_payment_status":{"type":"string","enum":["pending","awaiting confirmation","successful"]}}}}},"PartnerPaymentListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/PartnerPayment"}}},"PartnerPaymentListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/PartnerPaymentListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"PartnerPayment":{"type":"object","properties":{"partner_code":{"type":"string"},"payment_date":{"type":"string","format":"date"},"flw_payload":{"type":"string"},"currency":{"type":"string"},"status":{"type":"string","enum":["pending","successful","failed"]},"period":{"type":"string","enum":["weekly","monthly","quarterly","yearly","one-time"]},"period_start":{"type":"string","format":"date"},"period_end":{"type":"string","format":"date"},"txn_ref":{"type":"string"},"payment_link":{"type":"string"},"description":{"type":"string"},"amount":{"type":"number","format":"float"},"offline_payment_status":{"type":"string","enum":["pending","awaiting confirmation","successful"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"PartnerPaymentResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/PartnerPayment"}}},"PartnerPaymentResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/PartnerPaymentResponseDataObject"},"meta":{"type":"object"}}},"PetraCampusRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"location":{"type":"string"},"address":{"type":"string"},"resident_pastor":{"type":"string"},"meeting_days":{"type":"string"},"long":{"type":"number","format":"float"},"lat":{"type":"number","format":"float"},"leaders_bio":{"type":"string"},"contact_email":{"type":"string","format":"email"},"contact_phone":{"type":"string"},"resident_pastor_img":{"type":"string"},"slug":{"type":"string"},"instagram":{"type":"string"},"map_img":{"type":"string"},"campus_banner":{"type":"string"},"order":{"type":"integer"}}}}},"PetraCampusListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/PetraCampus"}}},"PetraCampusListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/PetraCampusListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"PetraCampus":{"type":"object","properties":{"location":{"type":"string"},"address":{"type":"string"},"resident_pastor":{"type":"string"},"meeting_days":{"type":"string"},"long":{"type":"number","format":"float"},"lat":{"type":"number","format":"float"},"leaders_bio":{"type":"string"},"contact_email":{"type":"string","format":"email"},"contact_phone":{"type":"string"},"resident_pastor_img":{"type":"string"},"slug":{"type":"string"},"instagram":{"type":"string"},"map_img":{"type":"string"},"campus_banner":{"type":"string"},"order":{"type":"integer"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"PetraCampusResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/PetraCampus"}}},"PetraCampusResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/PetraCampusResponseDataObject"},"meta":{"type":"object"}}},"RebateRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"payment_id":{"type":"integer"},"amount":{"type":"number","format":"float"},"reason":{"type":"string"}}}}},"RebateListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Rebate"}}},"RebateListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/RebateListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Rebate":{"type":"object","properties":{"payment_id":{"type":"integer"},"amount":{"type":"number","format":"float"},"reason":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"RebateResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Rebate"}}},"RebateResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/RebateResponseDataObject"},"meta":{"type":"object"}}},"ResourceRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"image":{"type":"string"},"download_link":{"type":"string"},"category":{"type":"string"}}}}},"ResourceListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Resource"}}},"ResourceListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ResourceListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Resource":{"type":"object","properties":{"name":{"type":"string"},"image":{"type":"string"},"download_link":{"type":"string"},"category":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"ResourceResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/Resource"}}},"ResourceResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ResourceResponseDataObject"},"meta":{"type":"object"}}},"SomFeeRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"amount":{"type":"number","format":"float"},"installments":{"type":"integer"},"session":{"type":"string"}}}}},"SomFeeListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomFee"}}},"SomFeeListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/SomFeeListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"SomFee":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"amount":{"type":"number","format":"float"},"installments":{"type":"integer"},"session":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"SomFeeResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomFee"}}},"SomFeeResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/SomFeeResponseDataObject"},"meta":{"type":"object"}}},"SomMessageRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"channel":{"type":"string"},"status":{"type":"string","enum":["success","pending","failed"]},"description":{"type":"string"},"recipient_id":{"type":"integer"}}}}},"SomMessageListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomMessage"}}},"SomMessageListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/SomMessageListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"SomMessage":{"type":"object","properties":{"channel":{"type":"string"},"status":{"type":"string","enum":["success","pending","failed"]},"description":{"type":"string"},"recipient_id":{"type":"integer"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"SomMessageResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomMessage"}}},"SomMessageResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/SomMessageResponseDataObject"},"meta":{"type":"object"}}},"SomPaymentRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"payer_id":{"type":"integer"},"payer_type":{"type":"string"},"status":{"type":"string"},"amount":{"type":"number","format":"float"},"amount_paid":{"type":"number","format":"float"},"due_at":{"type":"string","format":"date"},"currency":{"type":"string"},"reference":{"type":"string"},"description":{"type":"string"}}}}},"SomPaymentListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomPayment"}}},"SomPaymentListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/SomPaymentListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"SomPayment":{"type":"object","properties":{"payer_id":{"type":"integer"},"payer_type":{"type":"string"},"status":{"type":"string"},"amount":{"type":"number","format":"float"},"amount_paid":{"type":"number","format":"float"},"due_at":{"type":"string","format":"date"},"currency":{"type":"string"},"reference":{"type":"string"},"description":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"SomPaymentResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomPayment"}}},"SomPaymentResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/SomPaymentResponseDataObject"},"meta":{"type":"object"}}},"SomRegistrationRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"surname":{"type":"string"},"other_names":{"type":"string"},"phone_number":{"type":"string"},"email":{"type":"string","format":"email"},"dob":{"type":"string","format":"date"},"address":{"type":"string"},"occupation":{"type":"string"},"marital_status":{"type":"string"},"wedding_anniversary":{"type":"string","format":"date"},"no_of_children":{"type":"integer"},"salvation_story":{"type":"string"},"call":{"type":"string"},"ministry_experience":{"type":"string"},"joined_petra_at":{"type":"string","format":"date"},"joined_petra":{"type":"string"},"who_is_pastor_ayo_to_you":{"type":"string"},"commitment_to_petra":{"type":"string"},"code":{"type":"string"},"is_petra_member":{"type":"string","enum":["Yes","No"]},"photo_url":{"type":"string"},"how_did_you_find_out":{"type":"string"},"ministry":{"type":"string"},"position_in_ministry":{"type":"string"},"session":{"type":"string"}}}}},"SomRegistrationListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomRegistration"}}},"SomRegistrationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/SomRegistrationListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"SomRegistration":{"type":"object","properties":{"surname":{"type":"string"},"other_names":{"type":"string"},"phone_number":{"type":"string"},"email":{"type":"string","format":"email"},"dob":{"type":"string","format":"date"},"address":{"type":"string"},"occupation":{"type":"string"},"marital_status":{"type":"string"},"wedding_anniversary":{"type":"string","format":"date"},"no_of_children":{"type":"integer"},"salvation_story":{"type":"string"},"call":{"type":"string"},"ministry_experience":{"type":"string"},"joined_petra_at":{"type":"string","format":"date"},"joined_petra":{"type":"string"},"who_is_pastor_ayo_to_you":{"type":"string"},"commitment_to_petra":{"type":"string"},"code":{"type":"string"},"is_petra_member":{"type":"string","enum":["Yes","No"]},"photo_url":{"type":"string"},"how_did_you_find_out":{"type":"string"},"ministry":{"type":"string"},"position_in_ministry":{"type":"string"},"session":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"SomRegistrationResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomRegistration"}}},"SomRegistrationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/SomRegistrationResponseDataObject"},"meta":{"type":"object"}}},"SomSessionRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"session":{"type":"string"},"can_register":{"type":"boolean"},"start_date":{"type":"string","format":"date"},"end_date":{"type":"string","format":"date"},"group_chat_link":{"type":"string"},"venue":{"type":"string"}}}}},"SomSessionListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomSession"}}},"SomSessionListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/SomSessionListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"SomSession":{"type":"object","properties":{"session":{"type":"string"},"can_register":{"type":"boolean"},"start_date":{"type":"string","format":"date"},"end_date":{"type":"string","format":"date"},"group_chat_link":{"type":"string"},"venue":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"SomSessionResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomSession"}}},"SomSessionResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/SomSessionResponseDataObject"},"meta":{"type":"object"}}},"SomTransactionRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"payment_id":{"type":"integer"},"status":{"type":"string"},"channel":{"type":"string"},"currency":{"type":"string"},"amount":{"type":"number","format":"float"},"payer_id":{"type":"integer"},"description":{"type":"string"},"meta":{}}}}},"SomTransactionListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomTransaction"}}},"SomTransactionListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/SomTransactionListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"SomTransaction":{"type":"object","properties":{"payment_id":{"type":"integer"},"status":{"type":"string"},"channel":{"type":"string"},"currency":{"type":"string"},"amount":{"type":"number","format":"float"},"payer_id":{"type":"integer"},"description":{"type":"string"},"meta":{},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"SomTransactionResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/SomTransaction"}}},"SomTransactionResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/SomTransactionResponseDataObject"},"meta":{"type":"object"}}},"TribePetraKidRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"age":{"type":"integer"},"gender":{"type":"string","enum":["Male","Female"]}}}}},"TribePetraKidListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/TribePetraKid"}}},"TribePetraKidListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/TribePetraKidListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"TribePetraKid":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"age":{"type":"integer"},"gender":{"type":"string","enum":["Male","Female"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"TribePetraKidResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/TribePetraKid"}}},"TribePetraKidResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/TribePetraKidResponseDataObject"},"meta":{"type":"object"}}},"UpcomingProgrammeRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"title":{"type":"string"},"start_date":{"type":"string","format":"date"},"end_date":{"type":"string","format":"date"},"details":{"type":"string"}}}}},"UpcomingProgrammeListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/UpcomingProgramme"}}},"UpcomingProgrammeListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/UpcomingProgrammeListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"UpcomingProgramme":{"type":"object","properties":{"title":{"type":"string"},"start_date":{"type":"string","format":"date"},"end_date":{"type":"string","format":"date"},"details":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"UpcomingProgrammeResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/UpcomingProgramme"}}},"UpcomingProgrammeResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/UpcomingProgrammeResponseDataObject"},"meta":{"type":"object"}}},"VbsRegistrationRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"parent_first_name":{"type":"string"},"parent_last_name":{"type":"string"},"parent_email":{"type":"string","format":"email"},"parent_phone":{"type":"string"},"petra_member":{"type":"string","enum":["Yes","No"]},"number_of_children":{"type":"integer"},"children":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"VbsRegistrationListResponseDataItem":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/VbsRegistration"}}},"VbsRegistrationListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/VbsRegistrationListResponseDataItem"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"VbsRegistration":{"type":"object","properties":{"parent_first_name":{"type":"string"},"parent_last_name":{"type":"string"},"parent_email":{"type":"string","format":"email"},"parent_phone":{"type":"string"},"petra_member":{"type":"string","enum":["Yes","No"]},"number_of_children":{"type":"integer"},"children":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"surname":{"type":"string"},"age":{"type":"integer"},"gender":{"type":"string","enum":["Male","Female"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}},"permissions":{"type":"object","properties":{"data":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}},"updatedBy":{"type":"object","properties":{"data":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"type":"object","properties":{}}}}}}}},"VbsRegistrationResponseDataObject":{"type":"object","properties":{"id":{"type":"number"},"attributes":{"$ref":"#/components/schemas/VbsRegistration"}}},"VbsRegistrationResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/VbsRegistrationResponseDataObject"},"meta":{"type":"object"}}},"UploadFile":{"properties":{"id":{"type":"number"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"number","format":"integer"},"height":{"type":"number","format":"integer"},"formats":{"type":"number"},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"double"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{"type":"object"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-Role":{"type":"object","properties":{"id":{"type":"number"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-User":{"type":"object","properties":{"id":{"type":"number","example":1},"username":{"type":"string","example":"foo.bar"},"email":{"type":"string","example":"<EMAIL>"},"provider":{"type":"string","example":"local"},"confirmed":{"type":"boolean","example":true},"blocked":{"type":"boolean","example":false},"createdAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.258Z"},"updatedAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.267Z"}}},"Users-Permissions-UserRegistration":{"type":"object","properties":{"jwt":{"type":"string","example":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"},"user":{"$ref":"#/components/schemas/Users-Permissions-User"}}},"Users-Permissions-PermissionsTree":{"type":"object","additionalProperties":{"type":"object","description":"every api","properties":{"controllers":{"description":"every controller of the api","type":"object","additionalProperties":{"type":"object","additionalProperties":{"description":"every action of every controller","type":"object","properties":{"enabled":{"type":"boolean"},"policy":{"type":"string"}}}}}}}}},"requestBodies":{"Users-Permissions-RoleRequest":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"name":"foo","description":"role foo","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}}},"paths":{"/attendance-sheets":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AttendanceSheetListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Attendance-sheet"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/attendance-sheets"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AttendanceSheetResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Attendance-sheet"],"parameters":[],"operationId":"post/attendance-sheets","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/AttendanceSheetRequest"}}}}}},"/attendance-sheets/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AttendanceSheetResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Attendance-sheet"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/attendance-sheets/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AttendanceSheetResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Attendance-sheet"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/attendance-sheets/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/AttendanceSheetRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Attendance-sheet"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/attendance-sheets/{id}"}},"/blogs":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/blogs"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog"],"parameters":[],"operationId":"post/blogs","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogRequest"}}}}}},"/blogs/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/blogs/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/blogs/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/blogs/{id}"}},"/blogs/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/blogs/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogLocalizationRequest"}}}}}},"/care-groups":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CareGroupListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Care-group"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/care-groups"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CareGroupResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Care-group"],"parameters":[],"operationId":"post/care-groups","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CareGroupRequest"}}}}}},"/care-groups/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CareGroupResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Care-group"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/care-groups/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CareGroupResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Care-group"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/care-groups/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CareGroupRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Care-group"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/care-groups/{id}"}},"/church-members":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchMemberListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-member"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/church-members"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchMemberResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-member"],"parameters":[],"operationId":"post/church-members","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchMemberRequest"}}}}}},"/church-members/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchMemberResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-member"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/church-members/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchMemberResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-member"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/church-members/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchMemberRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-member"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/church-members/{id}"}},"/church-services":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchServiceListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-service"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/church-services"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchServiceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-service"],"parameters":[],"operationId":"post/church-services","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchServiceRequest"}}}}}},"/church-services/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchServiceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-service"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/church-services/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchServiceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-service"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/church-services/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChurchServiceRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Church-service"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/church-services/{id}"}},"/converts":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConvertListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Convert"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/converts"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConvertResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Convert"],"parameters":[],"operationId":"post/converts","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConvertRequest"}}}}}},"/converts/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConvertResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Convert"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/converts/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConvertResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Convert"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/converts/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConvertRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Convert"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/converts/{id}"}},"/directorates":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/DirectorateListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Directorate"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/directorates"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/DirectorateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Directorate"],"parameters":[],"operationId":"post/directorates","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/DirectorateRequest"}}}}}},"/directorates/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/DirectorateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Directorate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/directorates/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/DirectorateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Directorate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/directorates/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/DirectorateRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Directorate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/directorates/{id}"}},"/events":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/events"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[],"operationId":"post/events","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRequest"}}}}}},"/events/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/events/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/events/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/events/{id}"}},"/events/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/events/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventLocalizationRequest"}}}}}},"/event-attendances":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendance"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/event-attendances"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendance"],"parameters":[],"operationId":"post/event-attendances","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceRequest"}}}}}},"/event-attendances/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendance"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/event-attendances/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendance"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/event-attendances/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendance"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/event-attendances/{id}"}},"/event-attendances/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendance"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/event-attendances/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendanceLocalizationRequest"}}}}}},"/event-attendees":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendee"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/event-attendees"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendee"],"parameters":[],"operationId":"post/event-attendees","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeRequest"}}}}}},"/event-attendees/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/event-attendees/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/event-attendees/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/event-attendees/{id}"}},"/event-attendees/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-attendee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/event-attendees/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventAttendeeLocalizationRequest"}}}}}},"/event-registrations":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-registration"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/event-registrations"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-registration"],"parameters":[],"operationId":"post/event-registrations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationRequest"}}}}}},"/event-registrations/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/event-registrations/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/event-registrations/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/event-registrations/{id}"}},"/event-registrations/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/event-registrations/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventRegistrationLocalizationRequest"}}}}}},"/event-sessions":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-session"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/event-sessions"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-session"],"parameters":[],"operationId":"post/event-sessions","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionRequest"}}}}}},"/event-sessions/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/event-sessions/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/event-sessions/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/event-sessions/{id}"}},"/event-sessions/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Event-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/event-sessions/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EventSessionLocalizationRequest"}}}}}},"/hero-items":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Hero-item"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/hero-items"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Hero-item"],"parameters":[],"operationId":"post/hero-items","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemRequest"}}}}}},"/hero-items/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Hero-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/hero-items/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Hero-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/hero-items/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Hero-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/hero-items/{id}"}},"/hero-items/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Hero-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/hero-items/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HeroItemLocalizationRequest"}}}}}},"/international-delegates":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/InternationalDelegateListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["International-delegate"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/international-delegates"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/InternationalDelegateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["International-delegate"],"parameters":[],"operationId":"post/international-delegates","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/InternationalDelegateRequest"}}}}}},"/international-delegates/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/InternationalDelegateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["International-delegate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/international-delegates/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/InternationalDelegateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["International-delegate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/international-delegates/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/InternationalDelegateRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["International-delegate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/international-delegates/{id}"}},"/menu-items":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Menu-item"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/menu-items"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Menu-item"],"parameters":[],"operationId":"post/menu-items","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemRequest"}}}}}},"/menu-items/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Menu-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/menu-items/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Menu-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/menu-items/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Menu-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/menu-items/{id}"}},"/menu-items/{id}/localizations":{"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemLocalizationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Menu-item"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"post/menu-items/{id}/localizations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MenuItemLocalizationRequest"}}}}}},"/partners":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/partners"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner"],"parameters":[],"operationId":"post/partners","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerRequest"}}}}}},"/partners/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/partners/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/partners/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/partners/{id}"}},"/partner-messages":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerMessageListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-message"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/partner-messages"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-message"],"parameters":[],"operationId":"post/partner-messages","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerMessageRequest"}}}}}},"/partner-messages/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/partner-messages/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/partner-messages/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerMessageRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/partner-messages/{id}"}},"/partner-payments":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerPaymentListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-payment"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/partner-payments"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerPaymentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-payment"],"parameters":[],"operationId":"post/partner-payments","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerPaymentRequest"}}}}}},"/partner-payments/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerPaymentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-payment"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/partner-payments/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerPaymentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-payment"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/partner-payments/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PartnerPaymentRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Partner-payment"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/partner-payments/{id}"}},"/petra-campuses":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PetraCampusListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Petra-campus"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/petra-campuses"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PetraCampusResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Petra-campus"],"parameters":[],"operationId":"post/petra-campuses","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PetraCampusRequest"}}}}}},"/petra-campuses/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PetraCampusResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Petra-campus"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/petra-campuses/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PetraCampusResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Petra-campus"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/petra-campuses/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PetraCampusRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Petra-campus"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/petra-campuses/{id}"}},"/rebates":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RebateListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Rebate"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/rebates"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RebateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Rebate"],"parameters":[],"operationId":"post/rebates","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RebateRequest"}}}}}},"/rebates/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RebateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Rebate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/rebates/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RebateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Rebate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/rebates/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RebateRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Rebate"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/rebates/{id}"}},"/resources":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Resource"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/resources"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Resource"],"parameters":[],"operationId":"post/resources","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceRequest"}}}}}},"/resources/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Resource"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/resources/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Resource"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/resources/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Resource"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/resources/{id}"}},"/som-fees":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomFeeListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-fee"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/som-fees"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomFeeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-fee"],"parameters":[],"operationId":"post/som-fees","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomFeeRequest"}}}}}},"/som-fees/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomFeeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-fee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/som-fees/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomFeeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-fee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/som-fees/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomFeeRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-fee"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/som-fees/{id}"}},"/som-messages":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomMessageListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-message"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/som-messages"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-message"],"parameters":[],"operationId":"post/som-messages","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomMessageRequest"}}}}}},"/som-messages/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/som-messages/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/som-messages/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomMessageRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/som-messages/{id}"}},"/som-payments":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomPaymentListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-payment"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/som-payments"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomPaymentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-payment"],"parameters":[],"operationId":"post/som-payments","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomPaymentRequest"}}}}}},"/som-payments/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomPaymentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-payment"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/som-payments/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomPaymentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-payment"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/som-payments/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomPaymentRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-payment"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/som-payments/{id}"}},"/som-registrations":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomRegistrationListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-registration"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/som-registrations"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-registration"],"parameters":[],"operationId":"post/som-registrations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomRegistrationRequest"}}}}}},"/som-registrations/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/som-registrations/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/som-registrations/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomRegistrationRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/som-registrations/{id}"}},"/som-sessions":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomSessionListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-session"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/som-sessions"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-session"],"parameters":[],"operationId":"post/som-sessions","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomSessionRequest"}}}}}},"/som-sessions/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/som-sessions/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/som-sessions/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomSessionRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/som-sessions/{id}"}},"/som-transactions":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomTransactionListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-transaction"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/som-transactions"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomTransactionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-transaction"],"parameters":[],"operationId":"post/som-transactions","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomTransactionRequest"}}}}}},"/som-transactions/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomTransactionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-transaction"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/som-transactions/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomTransactionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-transaction"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/som-transactions/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SomTransactionRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Som-transaction"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/som-transactions/{id}"}},"/tribe-petra-kids":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TribePetraKidListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Tribe-petra-kid"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/tribe-petra-kids"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TribePetraKidResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Tribe-petra-kid"],"parameters":[],"operationId":"post/tribe-petra-kids","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TribePetraKidRequest"}}}}}},"/tribe-petra-kids/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TribePetraKidResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Tribe-petra-kid"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/tribe-petra-kids/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TribePetraKidResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Tribe-petra-kid"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/tribe-petra-kids/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TribePetraKidRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Tribe-petra-kid"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/tribe-petra-kids/{id}"}},"/upcoming-programme":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UpcomingProgrammeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Upcoming-programme"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/upcoming-programme"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UpcomingProgrammeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Upcoming-programme"],"parameters":[],"operationId":"put/upcoming-programme","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/UpcomingProgrammeRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Upcoming-programme"],"parameters":[],"operationId":"delete/upcoming-programme"}},"/vbs-registrations":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/VbsRegistrationListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Vbs-registration"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object"},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/vbs-registrations"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/VbsRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Vbs-registration"],"parameters":[],"operationId":"post/vbs-registrations","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/VbsRegistrationRequest"}}}}}},"/vbs-registrations/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/VbsRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Vbs-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/vbs-registrations/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/VbsRegistrationResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Vbs-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/vbs-registrations/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/VbsRegistrationRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Vbs-registration"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/vbs-registrations/{id}"}},"/upload":{"post":{"description":"Upload files","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"required":["files"],"type":"object","properties":{"path":{"type":"string","description":"The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3)."},"refId":{"type":"string","description":"The ID of the entry which the file(s) will be linked to"},"ref":{"type":"string","description":"The unique ID (uid) of the model which the file(s) will be linked to (api::restaurant.restaurant)."},"field":{"type":"string","description":"The field of the entry which the file(s) will be precisely linked to."},"files":{"type":"array","items":{"type":"string","format":"binary"}}}}}}}}},"/upload?id={id}":{"post":{"parameters":[{"name":"id","in":"query","description":"File id","required":true,"schema":{"type":"string"}}],"description":"Upload file information","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"type":"object","properties":{"fileInfo":{"type":"object","properties":{"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"}}},"files":{"type":"string","format":"binary"}}}}}}}},"/upload/files":{"get":{"tags":["Upload - File"],"responses":{"200":{"description":"Get a list of files","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}}}},"/upload/files/{id}":{"get":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Get a specific file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}},"delete":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Delete a file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}}},"/connect/{provider}":{"get":{"parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string","pattern":".*"}}],"tags":["Users-Permissions - Auth"],"summary":"Login with a provider","description":"Redirects to provider login before being redirect to /auth/{provider}/callback","responses":{"301":{"description":"Redirect response"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Local login","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"identifier":{"type":"string"},"password":{"type":"string"}}},"example":{"identifier":"foobar","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Connection","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local/register":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Register a user","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"username":{"type":"string"},"email":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foobar","email":"<EMAIL>","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Successful registration","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/{provider}/callback":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Default Callback from provider auth","parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string"}}],"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/forgot-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send rest password email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}},"example":{"email":"<EMAIL>"}}}},"responses":{"200":{"description":"Returns ok","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/reset-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Rest user password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"password":{"type":"string"},"passwordConfirmation":{"type":"string"},"code":{"type":"string"}}},"example":{"password":"Test1234","passwordConfirmation":"Test1234","code":"zertyoaizndoianzodianzdonaizdoinaozdnia"}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/change-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Update user's own password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["password","currentPassword","passwordConfirmation"],"properties":{"password":{"type":"string"},"currentPassword":{"type":"string"},"passwordConfirmation":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/email-confirmation":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Confirm user email","parameters":[{"in":"query","name":"confirmation","schema":{"type":"string"},"description":"confirmation token received by email"}],"responses":{"301":{"description":"Redirects to the configure email confirmation redirect url"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/send-email-confirmation":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send confirmation email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns email and boolean to confirm email was sent","content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"},"sent":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/permissions":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get default generated permissions","responses":{"200":{"description":"Returns the permissions tree","content":{"application/json":{"schema":{"type":"object","properties":{"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}},"controllerB":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"List roles","responses":{"200":{"description":"Returns list of roles","content":{"application/json":{"schema":{"type":"object","properties":{"roles":{"type":"array","items":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-Role"},{"type":"object","properties":{"nb_users":{"type":"number"}}}]}}}},"example":{"roles":[{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","nb_users":0}]}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a role","requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was create","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a role","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns the role","content":{"application/json":{"schema":{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}},"example":{"role":{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{role}":{"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was udpated","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns ok if the role was delete","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get list of users","responses":{"200":{"description":"Returns an array of users","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Users-Permissions-User"}},"example":[{"id":9,"username":"<EMAIL>","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-06-01T18:32:35.211Z","updatedAt":"2022-06-01T18:32:35.217Z"}]}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a user","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"201":{"description":"Returns created user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns a user","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"200":{"description":"Returns updated user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns deleted user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/me":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get authenticated user info","responses":{"200":{"description":"Returns user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/count":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get user count","responses":{"200":{"description":"Returns a number","content":{"application/json":{"schema":{"type":"number"},"example":1}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}}},"tags":[{"name":"Users-Permissions - Auth","description":"Authentication endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}},{"name":"Users-Permissions - Users & Roles","description":"Users, roles, and permissions endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}]},
          dom_id: '#swagger-ui',
          docExpansion: "none",
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset,
          ],
          plugins: [
            SwaggerUIBundle.plugins.DownloadUrl,
          ],
          layout: "StandaloneLayout",
        });

        window.ui = ui;
      }
    </script>

    <script src="/plugins/documentation/swagger-ui-bundle.js"></script>
    <script src="/plugins/documentation/swagger-ui-standalone-preset.js"></script>
  </body>
</html>
