export default {
  /**
   * Generate Partner Periodic Transaction
   * Every monday at 1am.
   * Cron format: Minute(0-59) Hour (0 - 23) Day of Month (1 - 31) Month (1 - 12) Day of Week (0 - 7)
   */
  generatePartnerPeriodicTransaction: {
    task: ({ strapi }) => {
      strapi.log.info("Generating partner periodic transactions...");
      strapi.services['api::partner-api.partner-api'].generatePeriodicTransactions();
    },
    options: {
      rule: "0 0 1 * * 1", // Every monday at 1am
      // rule: "*/1 * * * *", // Every minute
    },
  },
  /**
   * Generate Partner Periodic Transaction
   * Every Friday at 11pm.
   */
  markOfflineTransactionsComplete: {
    task: ({ strapi }) => {
      strapi.log.info("Marking offline transactions as complete...");
      strapi.services['api::partner-api.partner-api'].markOfflineTransactionsComplete();
    },
    options: {
      rule: "0 23 * * 5", // Every Friday at 11pm
    },
  },
};
