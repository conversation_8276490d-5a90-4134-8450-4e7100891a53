export default ({ env }) => ({
    "users-permissions": {
      config: {
        register: {
          allowedFields: ["phone", "roles"],
        },
        jwt: {
          expiresIn: '12h',
        },
      },
    },
    email: {
      config: {
        provider: 'strapi-provider-email-resend',
        providerOptions: {
          apiKey: env('RESEND_API_KEY'), // Required
        },
        settings: {
          defaultFrom: '<EMAIL>',
          defaultReplyTo: '<EMAIL>',
          testAddress: '<EMAIL>',
        },
      },
    },
  });
