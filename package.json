{"name": "petracc-strapi-be", "private": true, "version": "0.2.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-documentation": "5.6.0", "@strapi/plugin-users-permissions": "5.6.0", "@strapi/provider-email-sendgrid": "^4.15.4", "@strapi/strapi": "5.6.0", "axios": "^1.6.4", "flutterwave-node-v3": "^1.1.6", "form-data": "^4.0.0", "mailgun.js": "^10.1.0", "pg": "8.11.3", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "sharp": "^0.32.6", "strapi-provider-email-resend": "^1.0.4", "styled-components": "^6.0.0", "superagent": "^8.1.2"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "266cb275-11e6-4510-a386-ef56e0b26928"}, "license": "MIT"}